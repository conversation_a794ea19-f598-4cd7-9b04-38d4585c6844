package main

import (
	"fmt"

	"github.com/gofrs/uuid"
	"github.com/knadh/listmonk/models"
	"github.com/lib/pq"
	push "github.com/phuslu/log"
	"gopkg.in/volatiletech/null.v6"
)

// runnerDB implements runner.DataSource over the primary
// database.
type runnerDB struct {
	queries *models.Queries
}

func newManagerStore(q *models.Queries) *runnerDB {
	return &runnerDB{
		queries: q,
	}
}

// NextCampaigns retrieves active campaigns ready to be processed.
func (r *runnerDB) NextCampaigns(excludeIDs []int64) ([]*models.Campaign, error) {
	var out []*models.Campaign
	err := r.queries.NextCampaigns.Select(&out, pq.Int64Array(excludeIDs))
	return out, err
}

// NextCampaigns retrieves active campaigns ready to be processed.
func (r *runnerDB) NextCampaignsV2(cId int64) ([]*models.Campaign, error) {
	var out []*models.Campaign
	err := r.queries.NextCampaignsV2.Select(&out, cId)
	return out, err
}

func (r *runnerDB) GetActiveCampaign(cId int64) ([]*models.Campaign, error) {
	var out []*models.Campaign
	err := r.queries.GetActiveCampaign.Select(&out, cId)
	return out, err
}

// NextSubscribers retrieves a subset of subscribers of a given campaign.
// Since batches are processed sequentially, the retrieval is ordered by ID,
// and every batch takes the last ID of the last batch and fetches the next
// batch above that.
func (r *runnerDB) NextSubscribers(campID, limit int, catId int, channel string, prefDefault bool) ([]models.Subscriber, error) {
	var out []models.Subscriber
	err := r.queries.NextCampaignSubscribers.Select(&out, campID, limit, catId, channel, prefDefault)
	return out, err
}

// GetCampaign fetches a campaign from the database.
func (r *runnerDB) GetCampaign(campID int) (*models.Campaign, error) {
	var out = &models.Campaign{}
	err := r.queries.GetCampaign.Get(out, campID, nil, "default")
	return out, err
}

// UpdateToSendCount for campaign
func (r *runnerDB) HaltCampaign(process string, campID int) error {
	_, err := r.queries.HaltCampaign.Exec(process, campID)
	return err
}

// HaltCampaign
func (r *runnerDB) UpdateToSend(toSend int, campID int) error {
	_, err := r.queries.UpdateToSend.Exec(toSend, campID)
	return err
}

func (r *runnerDB) UpdateSentStatus(sent int, campID int) error {
	_, err := r.queries.UpdateSentStatus.Exec(sent, campID)
	return err
}

// UpdateCampaignStatus updates a campaign's status.
func (r *runnerDB) UpdateCampaignStatus(campID int, status string, updatedBy null.String) error {
	_, err := r.queries.UpdateCampaignStatus.Exec(campID, status, updatedBy)
	return err
}

func (r *runnerDB) PauseCampaign(campID int, isPaused bool, updatedBy null.String) error {
	_, err := r.queries.PauseCampaign.Exec(campID, isPaused, updatedBy)
	return err
}

// DeleteTargets deletes entry from campaign_targets.
// func (r *runnerDB) DeleteTargets(campID int) error {
// 	result, err := r.queries.DeleteTargets.Exec(campID)
// 	num, _ := result.RowsAffected()
// 	log.Printf("total %d columns deleted", num)
// 	return err
// }

// CreateLink registers a URL with a UUID for tracking clicks and returns the UUID.
func (r *runnerDB) CreateLink(url string) (string, error) {
	// Create a new UUID for the URL. If the URL already exists in the DB
	// the UUID in the database is returned.
	uu, err := uuid.NewV4()
	if err != nil {
		return "", err
	}

	var out string
	if err := r.queries.CreateLink.Get(&out, uu, url); err != nil {
		return "", err
	}

	return out, nil
}

// RecordBounce records a bounce event and returns the bounce count.
func (r *runnerDB) RecordBounce(b models.Bounce) (int64, int, error) {
	var res = struct {
		SubscriberID int64 `db:"subscriber_id"`
		Num          int   `db:"num"`
	}{}

	err := r.queries.UpdateCampaignStatus.Select(&res,
		b.SubscriberUUID,
		b.Email,
		b.CampaignUUID,
		b.Type,
		b.Source,
		b.Meta)

	return res.SubscriberID, res.Num, err
}

func (r *runnerDB) BlocklistSubscriber(id int64) error {
	_, err := r.queries.BlocklistSubscribers.Exec(pq.Int64Array{id})
	return err
}

func (r *runnerDB) DeleteSubscriber(id int64) error {
	_, err := r.queries.DeleteSubscribers.Exec(pq.Int64Array{id})
	return err
}

func (r *runnerDB) GetTemplateFromDb(id int) (*models.Template, error) {
	var out []models.Template
	r.queries.GetTemplateFromDb.Select(&out, id, false, "")
	if len(out) > 0 {
		return &out[0], nil
	}
	return nil, fmt.Errorf("no data found with templateId= %v", id)
}

func (r *runnerDB) GetCategories() ([]models.Category, error) {
	var out []models.Category

	if err := r.queries.GetCategories.Select(&out); err != nil {
		return nil, err
	}
	return out, nil
}

func (r *runnerDB) GetCategoriesById(id int) ([]models.Category, error) {
	var out []models.Category

	if err := r.queries.GetCategoriesById.Select(&out, id); err != nil {
		return out, err
	}

	if len(out) > 0 {
		return out, nil
	}

	return nil, fmt.Errorf("category with id %v not found", id)
}

func (r *runnerDB) CheckPreference(name string, typeName string, catId int, messenger string) (string, error) {
	var result []string

	err := r.queries.CheckPreference.Select(&result, name, typeName, catId, messenger)
	if err != nil {
		return "NotFound", err
	}
	if len(result) > 0 {
		return result[0], nil
	}
	return "NotFound", nil
}

func (r *runnerDB) UpdateCampaignDelivered(logger push.Logger, campaignId int, successDeliveries int64) error {
	result, err := r.queries.UpdateCampaignDelivered.Exec(campaignId, successDeliveries)
	if err != nil {
		logger.Error().Msgf("Failed to update delivered count for campaignID %d: %v", campaignId, err)
		return err
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		logger.Error().Msgf("Failed to fetch rows affected for campaignID %d: %v", campaignId, err)
		return err
	}

	if rowsAffected == 0 {
		logger.Warn().Msgf("No rows updated for campaignID %d ", campaignId)
	} else {
		logger.Info().Msgf("Successfully updated delivered count for campaignID %d. Rows affected: %d", campaignId, rowsAffected)
	}
	return nil
}

func (r *runnerDB) GetGatewayFromDb(id int) (*models.GateWayDetails, error) {
	var out []models.GateWayDetails
	r.queries.GetGateway.Select(&out, id)
	if len(out) > 0 {
		return &out[0], nil
	}
	return nil, fmt.Errorf("no data found with GatewayId= %v", id)
}

func (r *runnerDB) GetMessengerCount(messenger string) int {
	var count []int
	err := r.queries.GetMessengerCount.Select(&count, messenger)
	if err != nil || len(count) == 0 {
		return 0
	}
	return count[0]
}

func (r *runnerDB) GetNotificationFrequencyDetails() ([]models.NotificationFrequencies, error) {
	var out []models.NotificationFrequencies

	if err := r.queries.GetNotificationFrequencies.Select(&out); err != nil {
		return out, err
	}

	if len(out) > 0 {
		return out, nil
	}

	return nil, fmt.Errorf("notification frequencies not found")
}
