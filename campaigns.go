package main

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"html/template"
	"net/http"
	"net/url"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/gofrs/uuid"
	"github.com/knadh/listmonk/external"
	"github.com/knadh/listmonk/models"
	"github.com/knadh/listmonk/tracer"
	"github.com/knadh/listmonk/utils"
	"github.com/labstack/echo/v4"
	"github.com/lib/pq"
	push "github.com/phuslu/log"
	"github.com/robfig/cron/v3"
	"gopkg.in/volatiletech/null.v6"
)

// campaignReq is a wrapper over the Campaign model for receiving
// campaign creation and update data from APIs.
type campaignReq struct {
	models.Campaign

	// Indicates if the "send_at" date should be written or set to null.
	SendLater bool `json:"send_later"`

	// This overrides Campaign.Lists to receive and
	// write a list of int IDs during creation and updation.
	// Campaign.Lists is JSONText for sending lists children
	// to the outside world.
	ListIDs []int `json:"lists"`

	//Complete list object Added by <PERSON>ali
	SegLists []models.List `json:"segLists"`

	// This is only relevant to campaign test requests.
	SubscriberEmails pq.StringArray `json:"subscribers"`
}

// campaignContentReq wraps params coming from API requests for converting
// campaign content formats.
type campaignContentReq struct {
	models.Campaign
	From string `json:"from"`
	To   string `json:"to"`
}

type IdWrapper struct {
	ID int64 `db:"id" json:"id"`
}

var (
	regexFromAddress = regexp.MustCompile(`(.+?)\s<(.+?)@(.+?)>`)
)

// This API has been deprecated, we're using the filter API instead
// handleGetCampaigns handles retrieval of campaigns.
func handleGetCampaigns(c echo.Context) error {
	var (
		app = c.Get("app").(*App)
		pg  = app.paginator.NewFromURL(c.Request().URL.Query())

		status    = c.QueryParams()["status"]
		query     = strings.TrimSpace(c.FormValue("query"))
		orderBy   = c.FormValue("order_by")
		order     = c.FormValue("order")
		noBody, _ = strconv.ParseBool(c.QueryParam("no_body"))
		logger    = c.Get("logger").(push.Logger)
	)

	res, total, err := app.core.QueryCampaigns(query, status, orderBy, order, pg.Offset, pg.Limit, logger)
	if err != nil {
		return err
	}

	if noBody {
		for i := 0; i < len(res); i++ {
			res[i].Body = ""
		}
	}

	var out models.PageResults
	if len(res) == 0 {
		out.Results = []models.Campaign{}
		return c.JSON(http.StatusOK, okResp{out})
	}

	// Meta.
	out.Query = query
	out.Results = res
	out.Total = total
	out.Page = pg.Page
	out.PerPage = pg.PerPage

	return c.JSON(http.StatusOK, okResp{out})
}

// handleGetCampaign handles retrieval of campaigns.
func handleGetCampaign(c echo.Context) error {
	var (
		app       = c.Get("app").(*App)
		id, _     = strconv.Atoi(c.Param("id"))
		noBody, _ = strconv.ParseBool(c.QueryParam("no_body"))
		logger    = c.Get("logger").(push.Logger)
	)

	out, err := app.core.GetCampaign(id, "", logger)
	if err != nil {
		return err
	}

	if noBody {
		out.Body = ""
	}

	return c.JSON(http.StatusOK, okResp{out})
}

// handlePreviewCampaign renders the HTML preview of a campaign body.
func handlePreviewCampaign(c echo.Context) error {
	var (
		app      = c.Get("app").(*App)
		id, _    = strconv.Atoi(c.Param("id"))
		tplID, _ = strconv.Atoi(c.FormValue("template_id"))
		logger   = c.Get("logger").(push.Logger)
	)

	if id < 1 {
		return echo.NewHTTPError(http.StatusBadRequest, app.i18n.T("globals.messages.invalidID"))
	}

	camp, err := app.core.GetCampaignForPreview(id, tplID, logger)
	if err != nil {
		return err
	}

	// There's a body in the request to preview instead of the body in the DB.
	if c.Request().Method == http.MethodPost {
		camp.ContentType = c.FormValue("content_type")
		camp.Body = c.FormValue("body")
	}

	// Use a dummy campaign ID to prevent views and clicks from {{ TrackView }}
	// and {{ TrackLink }} being registered on preview.
	camp.UUID = dummySubscriber.UUID
	if err := camp.CompileTemplate(app.manager.TemplateFuncs(&camp), false); err != nil {
		logger.Error().Msgf("error compiling template: %v", err)
		return echo.NewHTTPError(http.StatusBadRequest,
			app.i18n.Ts("templates.errorCompiling", "error", err.Error()))
	}

	// Render the message body.
	msg, err := app.manager.NewCampaignMessage(&camp, dummySubscriber, tracer.WrapEchoContextLogger(c), "")
	if err != nil {
		logger.Error().Msgf("error rendering message: %v", err)
		return echo.NewHTTPError(http.StatusBadRequest,
			app.i18n.Ts("templates.errorRendering", "error", err.Error()))
	}

	if camp.ContentType == models.CampaignContentTypePlain {
		return c.String(http.StatusOK, string(msg.Body()))
	}

	return c.HTML(http.StatusOK, string(msg.Body()))
}

// handleCampaignContent handles campaign content (body) format conversions.
func handleCampaignContent(c echo.Context) error {
	var (
		app   = c.Get("app").(*App)
		id, _ = strconv.Atoi(c.Param("id"))
	)

	if id < 1 {
		return echo.NewHTTPError(http.StatusBadRequest, app.i18n.T("globals.messages.invalidID"))
	}

	var camp campaignContentReq
	if err := c.Bind(&camp); err != nil {
		return err
	}

	out, err := camp.ConvertContent(camp.From, camp.To)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, err.Error())
	}

	return c.JSON(http.StatusOK, okResp{out})
}

// handleCreateCampaign handles campaign creation.
// Newly created campaigns are always drafts.
func handleCreateCampaign(c echo.Context) error {

	var (
		app    = c.Get("app").(*App)
		o      campaignReq
		logger = c.Get("logger").(push.Logger)
	)

	req := c.Request()
	if req != nil && req.Header != nil {
		userID := req.Header.Get("kcUserId")
		o.CreatedBy = null.StringFrom(userID)
	}

	start := time.Now()
	logger.Info().Msg("Starting campaign creation!")
	if err := c.Bind(&o); err != nil {
		return err
	}

	//Invalid Cron Expression check
	if err := validateAndParseCron(&o, c); err != nil {
		// Validation failed, error response is already handled in the validateAndParseCron function
		out := okResp{
			Data: err.Error(),
		}
		return c.JSON(http.StatusBadRequest, out)
	}

	// If the campaign's 'opt-in', prepare a default message.
	if o.Type == models.CampaignTypeOptin {
		op, err := makeOptinCampaignMessage(o, app, logger)
		if err != nil {
			return err
		}
		o = op
	} else if o.Type == "" {
		o.Type = models.CampaignTypeRegular
	}

	if o.ContentType == "" {
		o.ContentType = models.CampaignContentTypeRichtext
	}
	if o.Messenger == "" {
		o.Messenger = "email"
	}
	if o.DeDuplication {
		if o.DuplicationLevel.IsZero() {
			o.DuplicationLevel = null.StringFrom("device")
		}
	}

	// Validate.
	if c, err := validateCampaignFields(o, app); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, err.Error())
	} else {
		o = c
	}

	if o.ArchiveTemplateID == 0 {
		o.ArchiveTemplateID = o.TemplateID
	}

	//Ended by Deepali
	var out models.Campaign
	var err error
	var headers []string
	if o.Campaign.FilePath.Valid && o.Campaign.FilePath.String != "" {
		localFile := external.DownloadFromS3Bucket(config.ServerConfig.BucketName, o.Campaign.FilePath.String,
			strconv.Itoa(o.CampaignID), config.ServerConfig.AwsAccessKey, config.ServerConfig.AwsSecretKey,
			config.ServerConfig.Region, tracer.WrapEchoContextLogger(c))
		if strings.HasSuffix(localFile, ".csv") {
			headers = external.ReadCsvHeaders(localFile, logger)
		}
		out, err = app.core.CreateFileCampaign(o.Campaign, 0, models.CampaignStatusDraft, logger)
	} else if o.Campaign.ReportName.Valid && o.Campaign.ReportName.String != "" {
		out, err = app.core.CreateFileCampaign(o.Campaign, 0, models.CampaignStatusDraft, logger)
	} else {
		//Added by Deepali
		//Add segment in listmonk list
		listData, newLists, _ := CreateListExternal(o.SegLists, c)

		o.ListIDs = nil
		for _, item := range listData {
			o.ListIDs = append(o.ListIDs, item.Base.ID)
		}

		listDataCopy := make([]models.List, len(listData))
		copy(listDataCopy, listData)

		newListsCopy := make([]int, len(newLists))
		copy(newListsCopy, newLists)
		out, err = app.core.CreateCampaign(o.Campaign, o.ListIDs, logger)
		go addSegmentV2(listDataCopy, newListsCopy, tracer.WrapEchoContextLogger(c))
	}
	if err != nil {
		return err
	}

	//Added by Deepali
	_, subAttribs, _ := createCampaignAttribMaster(out, c, headers)
	if subAttribs != "" {
		out.SubAttribs = subAttribs
	} else {
		out.SubAttribs = "[]"
	}
	//Ended by Deepali
	logger.Info().Msgf("Ended campaign creation! %v", time.Since(start).Microseconds())
	return c.JSON(http.StatusOK, okResp{out})
}

/*
* Added for segment sync optimization
 */

func addSegmentV2(listData []models.List, newIds []int, ctx context.Context) {

	logger := ctx.Value(tracer.TRACE_LOGGER_KEY).(push.Logger)

	newIdsMap := make(map[int]int)

	for _, value := range newIds {
		newIdsMap[value] = value
	}

	for _, item := range listData {

		if _, ok := newIdsMap[item.ID]; !ok {
			continue
		}

		if item.Description == "Listmonk" {
			continue
		}

		segmentName := item.Name
		lastId := "-"

		logger.Info().Msgf("Segment sync started for segmentName %v, listId %v", segmentName, item.ID)
		start := time.Now()

		//update lists status as pending
		listData, err := updateListStatus(item.Name, "pending")

		if err != nil {
			logger.Info().Msgf("eror occured while scanning the segmentName %v detailed error: %v", item.Name, err)
			return
		}

		if listData == nil {
			logger.Info().Msgf("list not found in listmonk with name %v", item.Name)
			return
		}

		for {
			response, err := external.GetSegmentMembers(config.ServerConfig.SegmentServiceUrl, lastId, item.Name, config.ServerConfig.SegmentMembershipPageSize, logger, ctx)

			if len(response.MemberIds) == 0 {
				break
			}

			if lastId == response.LastId || len(response.LastId) == 0 {
				break
			} else {
				lastId = response.LastId
			}

			if err != nil {
				logger.Error().Msgf("error getting segment members api for segmentName %v, detailed error %v", segmentName, err)
				continue
			}
			subscriberIds, err := insertNewSubscriberGetIds(response, logger)

			if len(subscriberIds) == 0 {
				logger.Error().Msgf("error inserting new subscribers for segmentName %v, detailed error %v", segmentName, err)
				continue
			}
			subscriberLists := createSubscriberListEntity(subscriberIds, int64(item.ID))
			logger.Info().Msgf("inserting %d subscribers for list %v segmentName %v", len(subscriberIds), item.ID, segmentName)

			size, err := insertSubscriberLists(subscriberLists, logger)

			if err != nil {
				logger.Error().Msgf("error inserting new subscribers for segmentName %v, listId %v, detailed error %v", segmentName, item.ID, err)
				continue
			}

			logger.Info().Msgf("inserted %d subscribers for list %v segmentName %v", size, item.ID, segmentName)
		}

		// update list status as finished
		listData, err = updateListStatus(item.Name, "finished")

		if err != nil {
			logger.Info().Msgf("eror occured while scanning the segmentName %v detailed error: %v", item.Name, err)
			return
		}

		if listData == nil {
			logger.Info().Msgf("list not found in listmonk with name %v", item.Name)
			return
		}
		logger.Info().Msgf("Segment sync completed for listId %v, segmentName %v, time taken %v", item.ID, segmentName, time.Since(start).Seconds())

	}
}

func createSubscriberEntity(subNames map[string]bool, memberType string, logger push.Logger) []Subscriber {
	var subscribers []Subscriber
	for k := range subNames {
		subscriber := Subscriber{}
		subscriber.Name = k
		subscriber.Type = memberType
		uuid, err := uuid.NewV4()
		if err != nil {
			logger.Error().Msgf("Error while generating uuid for subscriber(name, type) (%v, %v)", k, memberType)
			continue
		}
		subscriber.UUID = uuid.String()
		subscribers = append(subscribers, subscriber)
	}
	return subscribers
}

func createSubscriberListEntity(subIds []int64, listId int64) []models.SubscriberLists {
	subscribers := []models.SubscriberLists{}
	for _, item := range subIds {
		subscriber := models.SubscriberLists{}
		subscriber.ListId = listId
		subscriber.SubscriberID = item
		subscribers = append(subscribers, subscriber)
	}
	return subscribers
}

func insertNewSubscriberGetIds(response models.SegmentMembershipResponse, logger push.Logger) ([]int64, error) {

	subMap := make(map[string]bool)

	for _, member := range response.MemberIds {
		subMap[member] = true
	}

	subQuery := getSubQueryStringV2(response.MemberIds, response.MemberType)

	subIds := []int64{}

	existingSubs, err := fetchSubscribers(subQuery)

	if err != nil {
		logger.Error().Msgf("Error fetching subscribers %v", err)
		return subIds, err
	}

	for _, subs := range existingSubs {
		delete(subMap, subs.Name)
		subIds = append(subIds, subs.ID)
	}

	if len(subMap) == 0 {
		return subIds, nil
	}

	subscribers := createSubscriberEntity(subMap, response.MemberType, logger)

	tx, err := db.Beginx()

	if err != nil {
		logger.Error().Msgf("Error starting transaction to add subscribers %v", err)
		return subIds, err
	}

	logger.Info().Msgf("inserting %d subscribers for segmentId %v", len(subscribers), response.Id)

	row, err := tx.NamedQuery(`INSERT INTO subscribers(uuid, name, type, status) values(:uuid, :name, :type,'enabled') on conflict(name, type) do update set updated_at = now() returning id`, subscribers)

	if err != nil {
		logger.Error().Msgf("Error occured %v", err)
		tx.Rollback()
		return subIds, err
	}
	defer row.Close()

	if err := row.Err(); err != nil {
		logger.Error().Msgf("Error occured while inserting subscribers %v", err)
		tx.Rollback()
		return subIds, err
	}

	for row.Next() {
		var res IdWrapper
		row.StructScan(&res)
		subIds = append(subIds, res.ID)

	}
	tx.Commit()
	logger.Info().Msgf("Successfully upsert %d subscribers ", len(subIds))
	return subIds, nil
}

func insertSubscriberLists(newSubscribers []models.SubscriberLists, logger push.Logger) (int64, error) {

	subIds := int64(0)
	tx, err := db.Beginx()
	if err != nil {
		logger.Error().Msgf("Error starting transaction to add preferences %v", err)
		return subIds, err
	}

	result, err := tx.NamedExec(`INSERT INTO subscriber_lists(subscriber_id, list_id, status, created_at, updated_at) values(:subscriber_id, :list_id, 'confirmed',now(), now()) on conflict(subscriber_id, list_id) do nothing`, newSubscribers)

	if err != nil {
		logger.Error().Msgf("Error occurred %v", err)
		tx.Rollback()
		return subIds, err
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		logger.Error().Msgf("Error occurred while retrieving rows affected %v", err)
		tx.Rollback()
		return subIds, err
	}

	tx.Commit()
	logger.Info().Msgf("Successfully upsert %d subscriber_list ", rowsAffected)
	return rowsAffected, nil
}

func createCampaignAttribMaster(o models.Campaign, c echo.Context, headers []string) (string, string, error) {
	var (
		app    = c.Get("app").(*App)
		logger = c.Get("logger").(push.Logger)
	)
	attribs := models.Atribs{}
	attribsList := []models.Atribs{}
	//cache atrributes
	attribs.Text = "cache accountNumber"
	attribs.Value = "{{ .Cache.AccountNumber }}"
	attribsList = append(attribsList, attribs)

	attribs.Text = "cache corporateId"
	attribs.Value = "{{ .Cache.CorporateId }}"
	attribsList = append(attribsList, attribs)

	attribs.Text = "cache legalName"
	attribs.Value = "{{ .Cache.LegalName }}"
	attribsList = append(attribsList, attribs)

	attribs.Text = "cache merchantId"
	attribs.Value = "{{ .Cache.MerchantId }}"
	attribsList = append(attribsList, attribs)

	attribs.Text = "cache terminalId"
	attribs.Value = "{{ .Cache.TerminalId }}"
	attribsList = append(attribsList, attribs)

	attribs.Text = "cache dbaName"
	attribs.Value = "{{ .Cache.DbaName }}"
	attribsList = append(attribsList, attribs)

	attribs.Text = "cache email"
	attribs.Value = "{{ .Cache.Email }}"
	attribsList = append(attribsList, attribs)

	attribs.Text = "cache mobileNumber"
	attribs.Value = "{{ .Cache.MobileNumber }}"
	attribsList = append(attribsList, attribs)

	attribs.Text = "cache city"
	attribs.Value = "{{ .Cache.City }}"
	attribsList = append(attribsList, attribs)

	attribs.Text = "cache state"
	attribs.Value = "{{ .Cache.State }}"
	attribsList = append(attribsList, attribs)

	attribs.Text = "cache pinCode"
	attribs.Value = "{{ .Cache.PinCode }}"
	attribsList = append(attribsList, attribs)

	attribs.Text = "cache mccCode"
	attribs.Value = "{{ .Cache.MccCode }}"
	attribsList = append(attribsList, attribs)

	attribs.Text = "cache area"
	attribs.Value = "{{ .Cache.Area }}"
	attribsList = append(attribsList, attribs)

	attribs.Text = "cache status"
	attribs.Value = "{{ .Cache.Status }}"
	attribsList = append(attribsList, attribs)

	attribs.Text = "cache PrimaryContactName"
	attribs.Value = "{{ .Cache.primaryContactName }}"
	attribsList = append(attribsList, attribs)

	//Real Time Seg Attributes

	response := external.GetRealTimeSegmentProperties(config.ServerConfig.SegmentServiceUrl, "Real Time Segment", tracer.WrapEchoContextLogger(c))
	for _, r := range response {
		res := r
		attribs.Text = "real time" + res
		attribs.Value = "{{ .RealTime." + res + " }}"
		attribsList = append(attribsList, attribs)
	}

	if len(headers) > 0 {
		for _, header := range headers {
			attribs.Text = "file " + header
			attribs.Value = "{{ .File." + header + " }}"
			attribsList = append(attribsList, attribs)
		}
	}

	m := models.CampaignAtribsMaster{}
	//append Subscriber attribs to campaigns attribs master
	app.core.AppendSubscriberAttribs(&o, logger, &attribsList)
	j := external.ConvertToJson(attribsList)
	if j != "" {
		m.Attribs = j
		m.CampaignId = o.ID
	}
	out, err := app.core.CreateCampaignAttribs(m, logger)
	if err != nil {
		logger.Error().Msgf("error campaign attributes: %v", err)
	}

	logger.Info().Msgf("Campaign attribs saved successfully")
	return out, o.SubAttribs, nil

}

//Ended by Deepali

// handleUpdateCampaign handles campaign modification.
// Campaigns that are done cannot be modified.
func handleUpdateCampaign(c echo.Context) error {
	var (
		app    = c.Get("app").(*App)
		id, _  = strconv.Atoi(c.Param("id"))
		logger = c.Get("logger").(push.Logger)
	)

	if id < 1 {
		return echo.NewHTTPError(http.StatusBadRequest, app.i18n.T("globals.messages.invalidID"))

	}

	cm, err := app.core.GetCampaign(id, "", logger)
	if err != nil {
		return err
	}

	if isCampaignalMutable(cm.Status) {
		return echo.NewHTTPError(http.StatusBadRequest, app.i18n.T("campaigns.cantUpdate"))
	}

	// Read the incoming params into the existing campaign fields from the DB.
	// This allows updating of values that have been sent whereas fields
	// that are not in the request retain the old values.
	o := campaignReq{Campaign: cm}
	if err := c.Bind(&o); err != nil {
		return err
	}

	if c, err := validateCampaignFields(o, app); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, err.Error())
	} else {
		o = c
	}

	//Invalid Cron Expression check
	if err := validateAndParseCron(&o, c); err != nil {
		// Validation failed, error response is already handled in the validateAndParseCron function
		return err
	}

	//Added for enabling segment sync new segment if selected
	//in update campaign interface

	listData, newLists, _ := CreateListExternal(o.SegLists, c)

	o.ListIDs = []int{}
	for _, item := range listData {
		o.ListIDs = append(o.ListIDs, item.Base.ID)
	}

	listDataCopy := make([]models.List, len(listData))
	copy(listDataCopy, listData)

	newListsCopy := make([]int, len(newLists))
	copy(newListsCopy, newLists)

	//ended here

	value, _ := json.Marshal(o.Campaign)
	logger.Info().Msgf("got request for update campaign : %s", string(value))
	jsonData, err := json.Marshal(o.KeyVal)
	if err != nil {
		logger.Error().Msgf("error: %v", err)
	}

	req := c.Request()
	if req != nil && req.Header != nil {
		userID := req.Header.Get("kcUserId")
		o.UpdatedBy = null.StringFrom(userID)
	}

	out, err := app.core.UpdateCampaign(id, o.Campaign, o.ListIDs, o.SendLater, string(jsonData), logger)
	if err != nil {
		logger.Error().Msgf("error occured while updating campaign: %v", err)
		return err
	}

	// Added members to segments if update successful
	go addSegmentV2(listDataCopy, newListsCopy, tracer.WrapEchoContextLogger(c))

	return c.JSON(http.StatusOK, okResp{out})
}

// handleUpdateCampaignStatus handles campaign status modification.
func handleUpdateCampaignStatus(c echo.Context) error {
	var (
		app    = c.Get("app").(*App)
		id, _  = strconv.Atoi(c.Param("id"))
		logger = c.Get("logger").(push.Logger)
	)

	if id < 1 {
		return echo.NewHTTPError(http.StatusBadRequest, app.i18n.T("globals.messages.invalidID"))
	}

	var o struct {
		Status string `json:"status"`
	}

	if err := c.Bind(&o); err != nil {
		return err
	}

	req := c.Request()
	var updatedBy null.String
	if req != nil && req.Header != nil {
		userID := req.Header.Get("kcUserId")
		updatedBy = null.StringFrom(userID)
	}

	out, err := app.core.UpdateCampaignStatus(id, o.Status, logger, updatedBy)
	if err != nil {
		return err
	}

	return c.JSON(http.StatusOK, okResp{out})
}

// handleUpdateCampaignArchive handles campaign status modification.
func handleUpdateCampaignArchive(c echo.Context) error {
	var (
		app    = c.Get("app").(*App)
		id, _  = strconv.Atoi(c.Param("id"))
		logger = c.Get("logger").(push.Logger)
	)

	req := struct {
		Archive    bool        `json:"archive"`
		TemplateID int         `json:"archive_template_id"`
		Meta       models.JSON `json:"archive_meta"`
	}{}

	// Get and validate fields.
	if err := c.Bind(&req); err != nil {
		return err
	}

	var updatedBy null.String
	if c.Request() != nil && c.Request().Header != nil {
		userID := c.Request().Header.Get("kcUserId")
		updatedBy = null.StringFrom(userID)
	}

	if err := app.core.UpdateCampaignArchive(id, req.Archive, req.TemplateID, req.Meta, logger, updatedBy); err != nil {
		return err
	}

	return c.JSON(http.StatusOK, okResp{true})
}

// handleDeleteCampaign handles campaign deletion.
// Only scheduled campaigns that have not started yet can be deleted.
func handleDeleteCampaign(c echo.Context) error {
	var (
		app    = c.Get("app").(*App)
		id, _  = strconv.Atoi(c.Param("id"))
		logger = c.Get("logger").(push.Logger)
	)

	if id < 1 {
		return echo.NewHTTPError(http.StatusBadRequest, app.i18n.T("globals.messages.invalidID"))
	}

	var updatedBy null.String
	if c.Request() != nil && c.Request().Header != nil {
		userID := c.Request().Header.Get("kcUserId")
		updatedBy = null.StringFrom(userID)
	}

	if err := app.core.DeleteCampaign(id, logger, updatedBy); err != nil {
		return err
	}

	return c.JSON(http.StatusOK, okResp{true})
}

// handleGetRunningCampaignStats returns stats of a given set of campaign IDs.
func handleGetRunningCampaignStats(c echo.Context) error {
	var (
		app    = c.Get("app").(*App)
		logger = c.Get("logger").(push.Logger)
	)

	out, err := app.core.GetRunningCampaignStats(logger)
	if err != nil {
		return err
	}

	if len(out) == 0 {
		return c.JSON(http.StatusOK, okResp{[]struct{}{}})
	}

	// Compute rate.
	for i, c := range out {
		if c.Started.Valid && c.UpdatedAt.Valid {
			diff := int(c.UpdatedAt.Time.Sub(c.Started.Time).Minutes())
			if diff < 1 {
				diff = 1
			}

			rate := c.Sent / diff
			if rate > c.Sent || rate > c.ToSend {
				rate = c.Sent
			}

			// Rate since the starting of the campaign.
			out[i].NetRate = rate

			// Realtime running rate over the last minute.
			out[i].Rate = app.manager.GetCampaignStatsV2(c.ID).SendRate
		}
	}

	return c.JSON(http.StatusOK, okResp{out})
}

func handlePauseCampaign(c echo.Context) error {
	var (
		app       = c.Get("app").(*App)
		campID, _ = strconv.Atoi(c.Param("id"))
	)

	var updatedBy null.String
	req := c.Request()
	if req != nil && req.Header != nil {
		userID := req.Header.Get("kcUserId")
		updatedBy = null.StringFrom(userID)
	}
	err := app.manager.PauseCampaign(campID, true, updatedBy)
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, "Unable to pause campaign: "+err.Error())
	}
	return c.JSON(http.StatusOK, okResp{true})
}

func handleResumeCampaign(c echo.Context) error {
	var (
		app       = c.Get("app").(*App)
		campID, _ = strconv.Atoi(c.Param("id"))
	)

	var updatedBy null.String
	req := c.Request()
	if req != nil && req.Header != nil {
		userID := req.Header.Get("kcUserId")
		updatedBy = null.StringFrom(userID)
	}
	err := app.manager.PauseCampaign(campID, false, updatedBy)
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, "Unable to resume campaign: "+err.Error())
	}
	return c.JSON(http.StatusOK, okResp{true})
}

// handleTestCampaign handles the sending of a campaign message to
// arbitrary subscribers for testing.
func handleTestCampaign(c echo.Context) error {
	var (
		app       = c.Get("app").(*App)
		campID, _ = strconv.Atoi(c.Param("id"))
		tplID, _  = strconv.Atoi(c.FormValue("template_id"))
		req       campaignReq
		logger    = c.Get("logger").(push.Logger)
	)

	if campID < 1 {
		return echo.NewHTTPError(http.StatusBadRequest, app.i18n.T("globals.messages.errorID"))
	}

	// Get and validate fields.
	if err := c.Bind(&req); err != nil {
		return err
	}

	// Validate.
	if c, err := validateCampaignFields(req, app); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, err.Error())
	} else {
		req = c
	}
	if len(req.SubscriberEmails) == 0 {
		return echo.NewHTTPError(http.StatusBadRequest, app.i18n.T("campaigns.noSubsToTest"))
	}

	// Get the subscribers.
	for i := 0; i < len(req.SubscriberEmails); i++ {
		//Added by Deepali
		//req.SubscriberEmails[i] = strings.ToLower(strings.TrimSpace(req.SubscriberEmails[i]))
		req.SubscriberEmails[i] = utils.EncryptData(req.SubscriberEmails[i], logger)
		//Ended by Deepali
	}

	subs, err := app.core.GetSubscribersByEmail(req.SubscriberEmails, logger)
	if err != nil {
		return err
	}

	// The campaign.
	camp, err := app.core.GetCampaignForPreview(campID, tplID, logger)
	if err != nil {
		return err
	}

	// Override certain values from the DB with incoming values.
	camp.Name = req.Name
	camp.Subject = req.Subject
	camp.FromEmail = req.FromEmail
	camp.Body = req.Body
	camp.AltBody = req.AltBody
	camp.Messenger = req.Messenger
	camp.ContentType = req.ContentType
	camp.Headers = req.Headers
	camp.TemplateID = req.TemplateID

	// Send the test messages.
	for _, s := range subs {
		sub := s
		if err := sendTestMessage(sub, &camp, app, tracer.WrapEchoContextLogger(c)); err != nil {
			logger.Error().Msgf("error sending test message: %v", err)
			return echo.NewHTTPError(http.StatusInternalServerError,
				app.i18n.Ts("campaigns.errorSendTest", "error", err.Error()))
		}
	}

	return c.JSON(http.StatusOK, okResp{true})
}

// handleGetCampaignViewAnalytics retrieves view counts for a campaign.
func handleGetCampaignViewAnalytics(c echo.Context) error {
	var (
		app = c.Get("app").(*App)

		typ    = c.Param("type")
		from   = c.QueryParams().Get("from")
		to     = c.QueryParams().Get("to")
		logger = c.Get("logger").(push.Logger)
	)

	ids, err := parseStringIDs(c.Request().URL.Query()["id"])
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest,
			app.i18n.Ts("globals.messages.errorInvalidIDs", "error", err.Error()))
	}

	if len(ids) == 0 {
		return echo.NewHTTPError(http.StatusBadRequest,
			app.i18n.Ts("globals.messages.missingFields", "name", "`id`"))
	}

	if !strHasLen(from, 10, 30) || !strHasLen(to, 10, 30) {
		return echo.NewHTTPError(http.StatusBadRequest, app.i18n.T("analytics.invalidDates"))
	}

	// Campaign link stats.
	if typ == "links" {
		out, err := app.core.GetCampaignAnalyticsLinks(ids, typ, from, to, logger)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, okResp{out})
	}

	// View, click, bounce stats.
	out, err := app.core.GetCampaignAnalyticsCounts(ids, typ, from, to, logger)
	if err != nil {
		return err
	}

	return c.JSON(http.StatusOK, okResp{out})
}

// sendTestMessage takes a campaign and a subscriber and sends out a sample campaign message.
func sendTestMessage(sub models.Subscriber, camp *models.Campaign, app *App, ctx context.Context) error {

	logger := ctx.Value(tracer.TRACE_LOGGER_KEY).(push.Logger)

	if err := camp.CompileTemplate(app.manager.TemplateFuncs(camp), false); err != nil {
		logger.Error().Msgf("error compiling template: %v", err)
		return echo.NewHTTPError(http.StatusInternalServerError,
			app.i18n.Ts("templates.errorCompiling", "error", err.Error()))
	}

	// Create a sample campaign message.
	msg, err := app.manager.NewCampaignMessage(camp, sub, ctx, "")
	if err != nil {
		logger.Error().Msgf("error rendering message: %v", err)
		return echo.NewHTTPError(http.StatusNotFound,
			app.i18n.Ts("templates.errorRendering", "error", err.Error()))
	}

	return app.manager.PushCampaignMessage(msg)
}

// validateCampaignFields validates incoming campaign field values.
func validateCampaignFields(c campaignReq, app *App) (campaignReq, error) {
	if c.FromEmail == "" {
		c.FromEmail = app.constants.FromEmail
	} else if !regexFromAddress.Match([]byte(c.FromEmail)) {
		if _, err := app.importer.SanitizeEmail(c.FromEmail); err != nil {
			return c, errors.New(app.i18n.T("campaigns.fieldInvalidFromEmail"))
		}
	}

	if !strHasLen(c.Name, 1, stdInputMaxLen) {
		return c, errors.New(app.i18n.T("campaigns.fieldInvalidName"))
	}
	if !strHasLen(c.Subject, 1, stdInputMaxLen) {
		return c, errors.New(app.i18n.T("campaigns.fieldInvalidSubject"))
	}

	// if !hasLen(c.Body, 1, bodyMaxLen) {
	// 	return c,errors.New("invalid length for `body`")
	// }

	// If there's a "send_at" date, it should be in the future.
	if c.SendAt.Valid {
		if c.SendAt.Time.Before(time.Now()) {
			return c, errors.New(app.i18n.T("campaigns.fieldInvalidSendAt"))
		}
	}

	if c.EndAt.Valid {
		if c.EndAt.Time.Before(time.Now()) {
			return c, errors.New("invalid date")
		}
	}

	if len(c.ListIDs) == 0 && (!c.FilePath.Valid || c.FilePath.String == "") && (!c.ReportName.Valid || c.ReportName.String == "") {
		return c, errors.New(app.i18n.T("campaigns.fieldInvalidListIDs"))
	}

	if !app.manager.HasMessenger(c.Messenger) {
		return c, errors.New(app.i18n.Ts("campaigns.fieldInvalidMessenger", "name", c.Messenger))
	}

	jsonData, err := json.Marshal(c.KeyVal)

	if err != nil {
		return c, errors.New(app.i18n.Ts("campaigns.fieldInvalidBody", "additionalValues", err.Error()))
	}

	c.FcmKeyval = jsonData

	camp := models.Campaign{Body: c.Body, TemplateBody: tplTag, FcmKeyval: jsonData}
	if err := c.CompileTemplate(app.manager.TemplateFuncs(&camp), false); err != nil {
		return c, errors.New(app.i18n.Ts("campaigns.fieldInvalidBody", "error", err.Error()))
	}

	if len(c.Headers) == 0 {
		c.Headers = make([]map[string]string, 0)
	}

	if len(c.ArchiveMeta) == 0 {
		c.ArchiveMeta = json.RawMessage("{}")
	}

	return c, nil
}

// isCampaignalMutable tells if a campaign's in a state where it's
// properties can be mutated.
func isCampaignalMutable(status string) bool {
	return status == models.CampaignStatusRunning ||
		status == models.CampaignStatusCancelled ||
		status == models.CampaignStatusFinished
}

// makeOptinCampaignMessage makes a default opt-in campaign message body.
func makeOptinCampaignMessage(o campaignReq, app *App, logger push.Logger) (campaignReq, error) {
	if len(o.ListIDs) == 0 {
		return o, echo.NewHTTPError(http.StatusBadRequest, app.i18n.T("campaigns.fieldInvalidListIDs"))
	}

	// Fetch double opt-in lists from the given list IDs.
	lists, err := app.core.GetListsByOptin(o.ListIDs, models.ListOptinDouble, logger)
	if err != nil {
		return o, err
	}

	// No opt-in lists.
	if len(lists) == 0 {
		return o, echo.NewHTTPError(http.StatusBadRequest, app.i18n.T("campaigns.noOptinLists"))
	}

	// Construct the opt-in URL with list IDs.
	listIDs := url.Values{}
	for _, l := range lists {
		listIDs.Add("l", l.UUID)
	}
	// optinURLFunc := template.URL("{{ OptinURL }}?" + listIDs.Encode())
	optinURLAttr := template.HTMLAttr(fmt.Sprintf(`href="{{ OptinURL }}%s"`, listIDs.Encode()))

	// Prepare sample opt-in message for the campaign.
	var b bytes.Buffer
	if err := app.notifTpls.tpls.ExecuteTemplate(&b, "optin-campaign", struct {
		Lists        []models.List
		OptinURLAttr template.HTMLAttr
	}{lists, optinURLAttr}); err != nil {
		logger.Error().Msgf("error compiling 'optin-campaign' template: %v", err)
		return o, echo.NewHTTPError(http.StatusBadRequest,
			app.i18n.Ts("templates.errorCompiling", "error", err.Error()))
	}

	o.Body = b.String()
	return o, nil
}

func UpdateFilePathForReportRequestId(reportRequestId string, filePath string, ctx context.Context) {
	logger := ctx.Value(tracer.TRACE_LOGGER_KEY).(push.Logger)
	stmt := "update campaigns set file_path=$2 where report_request_id=$1 returning id"

	var campId int
	err := db.QueryRow(stmt, reportRequestId, filePath).Scan(&campId)
	if err != nil {
		logger.Error().Msgf("error occured %v", err)
	}

	logger.Info().Msgf("Inserted file path %v for camp with ID %d for reportRequestId %v", filePath, campId, reportRequestId)
}

func CronCampaignClone(id int, reportRequestId string, logger push.Logger) {
	uu, err := uuid.NewV4()
	if err != nil {
		logger.Error().Msgf("error generating UUID: %v", err)
	}

	timestamp := time.Now().Local().Format("2006-01-02 15:04:05")

	stmt := "insert into campaigns	(uuid ,  name, subject ,from_email , body , altbody , content_type  , headers , status ,tags , type ,    messenger  ,    template_id ,    to_send ,    sent ,    max_subscriber_id ,    last_subscriber_id ,	archive ,    archive_template_id ,    archive_meta ,    started_at ,    cron ,    fcm_image ,    fcm_keyval,		fcm_roles ,    category ,    fcm_cta, de_duplication, duplication_level, report_request_id , parent_campaign_uuid, file_path, campaign_type)	select  $1 ,name||'_'||$4, subject ,from_email , body , altbody , content_type  , headers , $3 ,		tags ,    type ,    messenger  ,    template_id ,    0 ,    0 ,    0 ,    0 ,		archive ,    archive_template_id ,    archive_meta ,    now() ,    null ,    fcm_image ,    fcm_keyval,		fcm_roles ,    category ,    fcm_cta, de_duplication, duplication_level, $5, uuid, file_path, campaign_type   from campaigns where id = $2 RETURNING id"

	var campId int
	err = db.QueryRow(stmt, uu, id, "running", timestamp, reportRequestId).Scan(&campId)
	if err != nil {
		logger.Error().Msgf("error occured %v", err)
	}

	logger.Info().Msgf("Inserted user with ID %d", campId)

	if campId != 0 {
		stmt, err := db.Prepare("INSERT INTO campaign_lists(campaign_id, list_id,list_name) select $1, list_id,list_name from campaign_lists where campaign_id=$2")
		if err != nil {
			panic(err.Error())
		}

		res, err := stmt.Exec(campId, id)
		if err != nil {
			panic(err.Error())
		}
		numRowsAffected, err := res.RowsAffected()
		if err != nil {
			panic(err.Error())
		}

		logger.Info().Msgf("Inserted %d rows into database.", numRowsAffected)

		stmt, err = db.Prepare("INSERT INTO campaign_attribs_master(campaign_id, attribs) select $1, attribs from campaign_attribs_master where campaign_id=$2")
		if err != nil {
			panic(err.Error())
		}

		res, err = stmt.Exec(campId, id)
		if err != nil {
			panic(err.Error())
		}
		numRowsAffected, err = res.RowsAffected()
		if err != nil {
			panic(err.Error())
		}

		logger.Info().Msgf("Inserted %d rows into database.\n", numRowsAffected)
	}
}

func validateAndParseCron(o *campaignReq, c echo.Context) error {
	if !o.Cron.IsZero() {
		logger := c.Get("logger").(push.Logger)
		exp := o.Cron.String
		parserV3 := cron.NewParser(cron.SecondOptional | cron.Minute | cron.Hour | cron.Dom | cron.Month | cron.Dow)

		_, err := parserV3.Parse(exp)

		if err != nil {
			logger.Error().Msgf("Error occurred while parsing expression: %v", err)

			return errors.New("cron expression is invalid")
		}
	}
	return nil
}

func handleGetCampaignFilter(c echo.Context) error {

	var (
		app    = c.Get("app").(*App)
		o      models.CampaignsSearchFilter
		logger = c.Get("logger").(push.Logger)
	)
	logger.Info().Msg("handleGetCampaignFilter!")

	if err := c.Bind(&o); err != nil {
		return err
	}

	pg := app.paginator.New(o.PageNo, o.PageSize)

	if o.CreatedFrom == "" && o.CreatedAt != "" {
		o.CreatedFrom = o.CreatedAt
	}

	if o.StartedFrom == "" && o.Started != "" {
		o.StartedFrom = o.Started
	}

	if o.EndFrom == "" && o.EndAt != "" {
		o.EndFrom = o.EndAt
	}
	res, total, err := app.core.QueryCampaignsWithFilter(o, pg.Offset, pg.Limit, logger)

	if err != nil {
		return err
	}

	var out models.PageResults
	if len(res) == 0 {
		out.Results = []models.Campaign{}
		return c.JSON(http.StatusOK, okResp{out})
	}

	//Meta.
	out.Query = o.SearchQuery
	out.Results = res
	out.Total = total
	out.Page = pg.Page
	out.PerPage = pg.PerPage

	return c.JSON(http.StatusOK, okResp{out})

}
