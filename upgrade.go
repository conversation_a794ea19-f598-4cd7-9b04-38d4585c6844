package main

import (
	"fmt"

	"strings"
	"time"

	"github.com/jmoiron/sqlx"
	"github.com/knadh/koanf"
	"github.com/knadh/listmonk/internal/migrations"
	"github.com/knadh/stuffbin"
	"github.com/lib/pq"
	"golang.org/x/mod/semver"
)

// migFunc represents a migration function for a particular version.
// fn (generally) executes database migrations and additionally
// takes the filesystem and config objects in case there are additional bits
// of logic to be performed before executing upgrades. fn is idempotent.
type migFunc struct {
	version string
	fn      func(*sqlx.DB, stuffbin.FileSystem, *koanf.Koanf) error
}

// migList is the list of available migList ordered by the semver.
// Each migration is a Go file in internal/migrations named after the semver.
// The functions are named as: v0.7.0 => migrations.V0_7_0() and are idempotent.
var migList = []migFunc{
	{"v0.4.0", migrations.V0_4_0},
	{"v0.7.0", migrations.V0_7_0},
	{"v0.8.0", migrations.V0_8_0},
	{"v0.9.0", migrations.V0_9_0},
	{"v1.0.0", migrations.V1_0_0},
	{"v2.0.0", migrations.V2_0_0},
	{"v2.1.0", migrations.V2_1_0},
	{"v2.2.0", migrations.V2_2_0},
	{"v2.3.0", migrations.V2_3_0},
	{"v2.4.0", migrations.V2_4_0},
	{"v2.4.1", migrations.V2_4_1},
	{"v2.5.0", migrations.V2_5_0},
	{"v2.5.1", migrations.V2_5_1},
	{"v2.6.0", migrations.V2_6_0},
	{"v2.6.1", migrations.V2_6_1},
	{"v2.6.2", migrations.V2_6_2},
	{"v2.6.3", migrations.V2_6_3},
	{"v2.6.4", migrations.V2_6_4},
	{"v2.6.5", migrations.V2_6_5},
	{"v2.6.6", migrations.V2_6_6},
	{"v2.6.7", migrations.V2_6_7},
	{"v2.6.8", migrations.V2_6_8},
	{"v2.6.9", migrations.V2_6_9},
	{"v2.7.0", migrations.V2_7_0},
	{"v2.8.0", migrations.V2_8_0},
	{"v2.8.1", migrations.V2_8_1},
	{"v2.9.0", migrations.V2_9_0},
	{"v2.9.1", migrations.V2_9_1},
	{"v2.10.0", migrations.V2_10_0},
	{"v3.0.0", migrations.V3_0_0},
	{"v3.0.1", migrations.V3_0_1},
	{"v3.0.2", migrations.V3_0_2},
	{"v3.0.3", migrations.V3_0_3},
	{"v3.0.4", migrations.V3_0_4},
	{"v3.0.5", migrations.V3_0_5},
	{"v3.0.6", migrations.V3_0_6},
	{"v3.0.7", migrations.V3_0_7},
	{"v3.0.8", migrations.V3_0_8},
	{"v4.0.0", migrations.V4_0_0},
	{"v4.0.1", migrations.V4_0_1},
	{"v4.0.2", migrations.V4_0_2},
	{"v4.0.3", migrations.V4_0_3},
	{"v4.0.4", migrations.V4_0_4},
	{"v4.0.5", migrations.V4_0_5},
	{"v4.0.6", migrations.V4_0_6},
	{"v4.0.7", migrations.V4_0_7},
	{"v4.0.8", migrations.V4_0_8},
	{"v4.0.9", migrations.V4_0_9},
	{"v4.0.10", migrations.V4_0_10},
	{"v4.0.11", migrations.V4_0_11},
	{"v4.1.0", migrations.V4_1_0},
	{"v4.2.0", migrations.V4_2_0},
	{"v4.3.0", migrations.V4_3_0},
}

// upgrade upgrades the database to the current version by running SQL migration files
// for all version from the last known version to the current one.
func upgrade(db *sqlx.DB, fs stuffbin.FileSystem, prompt bool) {

	lock := acquireLock(db)

	if lock {
		lo.Printf("DatabaseChangeLog: Successfully acquired the lock")
		time.Sleep(2 * time.Second)
	} else {
		lo.Printf("DatabaseChangeLog: Failed to acquire the lock, will wait until database changes are implemented")
		for !isLockTableEmpty(db) {
			time.Sleep(2 * time.Second)
		}

		return
	}

	if prompt {
		var ok string
		lo.Printf("** IMPORTANT: Take a backup of the database before upgrading.\n")
		fmt.Print("continue (y/n)?  ")
		if _, err := fmt.Scanf("%s", &ok); err != nil {
			lo.Fatal().Msgf("error reading value from terminal: %v", err)
		}
		if strings.ToLower(ok) != "y" {
			fmt.Println("upgrade cancelled")
			return
		}
	}

	_, toRun, err := getPendingMigrations(db)
	if err != nil {
		lo.Fatal().Msgf("error checking migrations: %v", err)
	}

	// No migrations to run.
	if len(toRun) == 0 {
		lo.Printf("no upgrades to run. Database is up to date.")
		releaseLock(db)
		return
	}

	// Execute migrations in succession.
	for _, m := range toRun {
		lo.Printf("running migration %s", m.version)
		if err := m.fn(db, fs, ko); err != nil {
			lo.Fatal().Msgf("error running migration %s: %v", m.version, err)
		}

		// Record the migration version in the settings table. There was no
		// settings table until v0.7.0, so ignore the no-table errors.
		if err := recordMigrationVersion(m.version, db); err != nil {
			if isTableNotExistErr(err) {
				continue
			}
			lo.Fatal().Msgf("error recording migration version %s: %v", m.version, err)
		}
	}

	lo.Printf("upgrade complete")
	releaseLock(db)
}

// checkUpgrade checks if the current database schema matches the expected
// binary version.
func checkUpgrade(db *sqlx.DB) {
	lastVer, toRun, err := getPendingMigrations(db)
	if err != nil {
		lo.Fatal().Msgf("error checking migrations: %v", err)
	}

	// No migrations to run.
	if len(toRun) == 0 {
		return
	}

	var vers []string
	for _, m := range toRun {
		vers = append(vers, m.version)
	}

	lo.Fatal().Msgf(`there are %d pending database upgrade(s): %v. The last upgrade was %s. Backup the database and run listmonk --upgrade`,
		len(toRun), vers, lastVer)
}

// getPendingMigrations gets the pending migrations by comparing the last
// recorded migration in the DB against all migrations listed in `migrations`.
func getPendingMigrations(db *sqlx.DB) (string, []migFunc, error) {
	lastVer, err := getLastMigrationVersion()
	if err != nil {
		return "", nil, err
	}

	// Iterate through the migration versions and get everything above the last
	// upgraded semver.
	var toRun []migFunc
	for i, m := range migList {
		if semver.Compare(m.version, lastVer) > 0 {
			toRun = migList[i:]
			break
		}
	}

	return lastVer, toRun, nil
}

// getLastMigrationVersion returns the last migration semver recorded in the DB.
// If there isn't any, `v0.0.0` is returned.
func getLastMigrationVersion() (string, error) {
	var v string
	if err := db.Get(&v, `
		SELECT COALESCE(
			(SELECT value->>-1 FROM settings WHERE key='migrations'),
		'v0.0.0')`); err != nil {
		if isTableNotExistErr(err) {
			return "v0.0.0", nil
		}
		return v, err
	}
	return v, nil
}

// isPqNoTableErr checks if the given error represents a Postgres/pq
// "table does not exist" error.
func isTableNotExistErr(err error) bool {
	if p, ok := err.(*pq.Error); ok {
		// `settings` table does not exist. It was introduced in v0.7.0.
		if p.Code == "42P01" {
			return true
		}
	}
	return false
}

/*
*Added by Sachin for on start database migrations
 */

func acquireLock(db *sqlx.DB) bool {

	createLockTable(db)
	// Start a transaction
	tx, err := db.Beginx()
	if err != nil {
		lo.Printf("DatabaseChangeLog: Error starting transaction: %v", err)
		return false
	}

	// Create the lock record if it doesn't exist (use ON CONFLICT DO NOTHING)
	insertSQL := `
		INSERT INTO databasechangeloglock (id, lockgranted)
		VALUES (1, NOW()) ON CONFLICT (id) do nothing returning id`
	rs, err := tx.Exec(insertSQL)
	if err != nil {
		lo.Printf("DatabaseChangeLog: Error inserting lock row: %v", err)
		tx.Rollback()
		return false
	}

	last, _ := rs.RowsAffected()

	if last <= 0 {
		tx.Commit()
		return false
	}

	if err := tx.Commit(); err != nil {
		lo.Printf("DatabaseChangeLog: Error committing transaction: %v", err)
		tx.Rollback()
		return false
	}
	return true
}

func releaseLock(db *sqlx.DB) {
	_, err := db.Exec("DELETE FROM databasechangeloglock WHERE id = 1")
	if err != nil {
		lo.Printf("DatabaseChangeLog: Error releasing lock: %v", err)
	} else {
		lo.Printf("DatabaseChangeLog: Successfully released lock")
	}
}

func createLockTable(db *sqlx.DB) {
	createTableSQL := `
		CREATE TABLE IF NOT EXISTS databasechangeloglock (
			id SERIAL PRIMARY KEY,
			lockgranted TIMESTAMP
		)
	`
	_, err := db.Exec(createTableSQL)
	if err != nil {
		lo.Printf("DatabaseChangeLog Error creating lock table: %v", err)
	}
}

func isLockTableEmpty(db *sqlx.DB) bool {
	var count int
	err := db.Get(&count, "SELECT COUNT(*) FROM databasechangeloglock")
	if err != nil {
		lo.Fatal().Msgf("DatabaseChangeLog checking lock table:", err)
		return false
	}
	return count == 0
}
