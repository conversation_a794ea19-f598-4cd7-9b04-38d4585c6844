package tracer

import (
	"context"
	"crypto/tls"
	"fmt"
	"net/http"
	"os"
	"strconv"
	"time"

	"github.com/knadh/listmonk/logger"
	"github.com/knadh/listmonk/outbound"
	"github.com/labstack/echo/v4"
	"github.com/nats-io/nats.go"
	"github.com/openzipkin/zipkin-go"
	zipkinhttp "github.com/openzipkin/zipkin-go/middleware/http"
	"github.com/openzipkin/zipkin-go/model"
	zipkinreporter "github.com/openzipkin/zipkin-go/reporter"
	"github.com/phuslu/log"
)

var (
	ZipkinTracer *zipkin.Tracer
	reporter     zipkinreporter.Reporter
)

type ctxKey struct{}

var TRACE_LOGGER_KEY = ctxKey{}

type msgKey struct{}

var MSG_ID_KEY = msgKey{}

var MSG_HEADER_KEY = "Campaign-Msg-Id"

func InitTracer(appName, appAddress string) {
	reporter = zipkinreporter.NewNoopReporter()

	// Create a Zipkin tracer
	localEndpoint, err := zipkin.NewEndpoint(appName, appAddress)
	if err != nil {
		fmt.Printf("Error creating Zipkin endpoint: %v\n", err)
		os.Exit(1)
	}

	ZipkinTracer, err = zipkin.NewTracer(reporter, zipkin.WithLocalEndpoint(localEndpoint))
	if err != nil {
		fmt.Printf("Error creating Zipkin tracer: %v\n", err)
		os.Exit(1)
	}
}

func CloseZipkinReporter() {
	reporter.Close()
}

func traceHandlerMiddleware(next echo.HandlerFunc) echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		span, ctx := ZipkinTracer.StartSpanFromContext(ctx, "API Request")
		traceID := span.Context().TraceID.String()
		spanID := span.Context().ID.String()
		msgId := c.Request().Header.Get(MSG_HEADER_KEY)
		if msgId != "" {
			ctx = context.WithValue(ctx, MSG_ID_KEY, msgId)
		}
		requestlogger := logger.Log
		requestlogger.Context = logger.NewContextWithTracerMsgId(traceID, spanID, msgId)
		c.Set("logger", requestlogger)
		c.SetRequest(c.Request().WithContext(zipkin.NewContext(ctx, span)))
		// c.Set("span", span)
		return next(c)
	}
}

func SetUpMiddleware(f echo.MiddlewareFunc) (echo.MiddlewareFunc, echo.MiddlewareFunc, echo.MiddlewareFunc) {
	serverMiddleware := zipkinhttp.NewServerMiddleware(
		ZipkinTracer, zipkinhttp.TagResponseSize(true),
	)
	return f, echo.WrapMiddleware(serverMiddleware), traceHandlerMiddleware
}

func GetHttpClient(sslcheck bool) (*zipkinhttp.Client, error) {
	b := &outbound.CustomTransport{
		T: &http.Transport{
			TLSClientConfig: &tls.Config{
				InsecureSkipVerify: sslcheck,
			},
		}}
	client := &http.Client{
		Transport: b,
	}
	zipkinClient := zipkinhttp.WithClient(client)
	return zipkinhttp.NewClient(ZipkinTracer, zipkinhttp.ClientTrace(true), zipkinClient)
}

func GetPostbackHttpClient(sslcheck bool, maxConns int, timeout time.Duration) (*zipkinhttp.Client, error) {
	client := &http.Client{
		Timeout: timeout,
		Transport: &http.Transport{
			MaxIdleConnsPerHost:   maxConns,
			MaxConnsPerHost:       maxConns,
			ResponseHeaderTimeout: timeout,
			IdleConnTimeout:       timeout,
			TLSClientConfig: &tls.Config{
				InsecureSkipVerify: sslcheck,
			},
		},
	}
	zipkinClient := zipkinhttp.WithClient(client)
	return zipkinhttp.NewClient(ZipkinTracer, zipkinhttp.ClientTrace(true), zipkinClient)
}

func GetRequestWithTraceEcho(req *http.Request, c echo.Context) *http.Request {
	span := zipkin.SpanFromContext(c.Request().Context())
	ctx := zipkin.NewContext(req.Context(), span)
	return req.WithContext(ctx)
}

func GetRequestWithTraceContext(req *http.Request, c context.Context) *http.Request {
	span := zipkin.SpanFromContext(c)
	ctx := zipkin.NewContext(req.Context(), span)
	return req.WithContext(ctx)
}

func GenerateNewTracingContext(c context.Context, name string, msgId string) context.Context {
	span, ctx := ZipkinTracer.StartSpanFromContext(c, name)
	traceID := span.Context().TraceID.String()
	spanID := span.Context().ID.String()
	requestlogger := logger.Log

	if msgId == "" {
		requestlogger.Context = logger.NewContextWithTracer(traceID, spanID)
		ctx = context.WithValue(ctx, MSG_ID_KEY, msgId)
	} else {
		requestlogger.Context = logger.NewContextWithTracerMsgId(traceID, spanID, msgId)
	}
	return context.WithValue(ctx, TRACE_LOGGER_KEY, requestlogger)
}

/*
 * GetNatsTracingContext function will fetch the reqired tracing params from headers
 * and then add them to current context
 * along with the logger
 *
 */

func GetNatsTracingContext(header nats.Header) context.Context {
	traceLowString := header.Get("TraceId")
	traceHighString := header.Get("TraceId-High")
	campaignMsgId := header.Get(MSG_HEADER_KEY)
	natsLogger := logger.Log

	if traceLowString == "" || traceHighString == "" {
		return GenerateNewTracingContext(context.Background(), "consumer", campaignMsgId)
	}

	traceId, err := strconv.ParseUint(traceLowString, 10, 64)
	if err != nil {
		natsLogger.Warn().Msgf("error while fetching the traceID %s from nats header occurred %v", traceLowString, err)
		traceIdInt, err := strconv.ParseInt(traceLowString, 10, 64)
		if err != nil {
			return GenerateNewTracingContext(context.Background(), "consumer", campaignMsgId)
		} else {
			traceId = uint64(traceIdInt)
		}
	}
	traceIdHigh, err := strconv.ParseUint(traceHighString, 10, 64)

	if err != nil {
		natsLogger.Warn().Msgf("error while fetching the traceIdHigh: %s detailed error %v", traceHighString, err)
		traceIdHighInt, err := strconv.ParseInt(traceLowString, 10, 64)
		if err != nil {
			return GenerateNewTracingContext(context.Background(), "consumer", campaignMsgId)
		} else {
			traceId = uint64(traceIdHighInt)
		}
	}

	span := ZipkinTracer.StartSpan("consumer", zipkin.Parent(
		model.SpanContext{
			TraceID: model.TraceID{
				High: traceIdHigh,
				Low:  traceId,
			},
		},
	))
	return GenerateNewTracingContext(zipkin.NewContext(context.Background(), span), "consumer", campaignMsgId)
}

func WrapEchoContextLogger(c echo.Context) context.Context {
	requestLog := c.Get("logger").(log.Logger)
	msgId := c.Request().Header.Get(MSG_HEADER_KEY)
	ctx := c.Request().Context()
	if msgId != "" {
		ctx = context.WithValue(c.Request().Context(), MSG_ID_KEY, msgId)
	}
	return context.WithValue(ctx, TRACE_LOGGER_KEY, requestLog)
}
