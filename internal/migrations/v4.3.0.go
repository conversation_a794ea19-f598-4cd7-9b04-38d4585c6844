package migrations

import (
	"github.com/jmoiron/sqlx"
	"github.com/knadh/koanf"
	"github.com/knadh/stuffbin"
)

func V4_3_0(db *sqlx.DB, fs stuffbin.FileSystem, ko *koanf.Koanf) error {

	if _, err := db.Exec(`
		CREATE INDEX idx_campaigns_fts ON campaigns USING gin(to_tsvector('english', name || ' ' || subject));`); err != nil {
		return err
	}

	if _, err := db.Exec(`
        CREATE EXTENSION IF NOT EXISTS pg_trgm;`); err != nil {
		return err
	}

	if _, err := db.Exec(`
        CREATE INDEX IF NOT EXISTS idx_campaigns_name_trgm ON campaigns USING gin(name gin_trgm_ops);`); err != nil {
		return err
	}

	if _, err := db.Exec(`
        CREATE INDEX IF NOT EXISTS idx_campaigns_subject_trgm ON campaigns USING gin(subject gin_trgm_ops);`); err != nil {
		return err
	}

	if _, err := db.Exec(`
        CREATE INDEX IF NOT EXISTS idx_campaigns_status ON campaigns (status);`); err != nil {
		return err
	}

	if _, err := db.Exec(`
        CREATE INDEX idx_campaigns_created_at ON campaigns (created_at)`); err != nil {
		return err
	}

	if _, err := db.Exec(`
        CREATE INDEX idx_campaigns_started_at ON campaigns (started_at)`); err != nil {
		return err
	}

	if _, err := db.Exec(`
        CREATE INDEX IF NOT EXISTS idx_campaigns_status_created_at ON campaigns (status, created_at)`); err != nil {
		return err
	}

	if _, err := db.Exec(`
        CREATE INDEX IF NOT EXISTS idx_campaigns_status_started_at ON campaigns (status, started_at)`); err != nil {
		return err
	}

	return nil
}
