package messengerlog

import (
	"fmt"
	"log"

	"github.com/gofrs/uuid"
	"github.com/jmoiron/sqlx"
	"github.com/knadh/listmonk/models"
)

func InsertMessengerLogs(db *sqlx.DB, lo *log.Logger, logs []models.MessengerLog) (int64, error) {
	if len(logs) == 0 {
		lo.Printf("No logs to insert")
		return 0, fmt.Errorf("no logs to insert")
	}

	// Generate a UUID for each log
	for i := range logs {
		uuid, err := uuid.NewV4()
		if err != nil {
			lo.Printf("error generating UUID: %v", err)
		}
		logs[i].UUID = uuid.String()
	}

	query := `
        INSERT INTO messenger_logs (
			uuid,
            request_type,
			messenger_type,
            member_name,
            member_type,
            target,
            status,
            remarks,
            response,
            reference_id,
            additional_details,
            updated_at,
            created_by,
            updated_by
        ) VALUES (
		 	:uuid,
            :request_type,
			:messenger_type,
            :member_name,
            :member_type,
            :target,
            :status,
            :remarks,
            :response,
            :reference_id,
            :additional_details,
            :updated_at,
            :created_by,
            :updated_by
        )
    `

	result, err := db.NamedExec(query, logs)
	if err != nil {
		lo.Printf("Failed to execute insert ", err.Error())
		return 0, err
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		lo.Printf("Failed to get rows affected")
		return 0, err
	}

	lo.Printf("Successfully inserted messenger logs with rows_inserted", rowsAffected)
	return rowsAffected, err
}
