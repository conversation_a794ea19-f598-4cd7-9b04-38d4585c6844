package manager

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"html/template"
	"log"
	"net/textproto"
	"regexp"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/jmoiron/sqlx"
	"github.com/knadh/listmonk/internal/cacheutils"
	"github.com/knadh/listmonk/internal/fcm"
	"github.com/knadh/listmonk/internal/messengerlog"
	"gopkg.in/volatiletech/null.v6"

	"github.com/Masterminds/sprig/v3"
	"github.com/google/uuid"
	"github.com/knadh/listmonk/authprovider"
	"github.com/knadh/listmonk/external"
	"github.com/knadh/listmonk/internal/i18n"
	"github.com/knadh/listmonk/internal/messenger"
	"github.com/knadh/listmonk/models"
	"github.com/knadh/listmonk/tracer"
	"github.com/knadh/listmonk/utils"
	"github.com/paulbellamy/ratecounter"
	push "github.com/phuslu/log"
)

const (
	// BaseTPL is the name of the base template.
	BaseTPL = "base"

	// ContentTpl is the name of the compiled message.
	ContentTpl = "content"

	dummyUUID = "00000000-0000-0000-0000-000000000000"
)

// Store represents a data backend, such as a database,
// that provides subscriber and campaign records.
type Store interface {
	NextCampaigns(excludeIDs []int64) ([]*models.Campaign, error)
	NextCampaignsV2(cId int64) ([]*models.Campaign, error)
	NextSubscribers(campID, limit int, catId int, channel string, prefDefault bool) ([]models.Subscriber, error)
	GetCampaign(campID int) (*models.Campaign, error)
	UpdateCampaignStatus(campID int, status string, updatedBy null.String) error
	PauseCampaign(campID int, isPaused bool, updatedBy null.String) error
	CreateLink(url string) (string, error)
	BlocklistSubscriber(id int64) error
	DeleteSubscriber(id int64) error
	//DeleteTargets(campId int) error
	GetActiveCampaign(campId int64) ([]*models.Campaign, error)
	HaltCampaign(process string, campID int) error
	GetTemplateFromDb(id int) (*models.Template, error)
	UpdateToSend(toSend int, campID int) error
	UpdateSentStatus(sent int, campID int) error
	GetCategories() ([]models.Category, error)
	GetCategoriesById(id int) ([]models.Category, error)
	CheckPreference(name string, typeName string, catId int, messenger string) (string, error)
	GetGatewayFromDb(id int) (*models.GateWayDetails, error)
	GetMessengerCount(messenger string) int
	UpdateCampaignDelivered(logger push.Logger, campaignId int, successDeliveries int64) error
	GetNotificationFrequencyDetails() ([]models.NotificationFrequencies, error)
}

// CampStats contains campaign stats like per minute send rate.
type CampStats struct {
	SendRate int
}

// Manager handles the scheduling, processing, and queuing of campaigns
// and message pushes.
type Manager struct {
	cfg        Config
	store      Store
	i18n       *i18n.I18n
	messengers map[string]messenger.Messenger
	notifCB    models.AdminNotifCallback
	logger     *log.Logger
	db         *sqlx.DB

	// Campaigns that are currently running.
	camps     map[int]*models.Campaign
	campRates map[int]*ratecounter.RateCounter
	campsMut  sync.RWMutex

	tpls    map[int]*models.Template
	tplsMut sync.RWMutex

	//Added by Sachin
	campaignMap map[int]*models.Campaign
	campMapMut  sync.RWMutex

	campSmsMap map[int]map[int]int
	campSmsMut sync.RWMutex

	//category map
	categoryMap map[int]models.Preference
	catMapMut   sync.RWMutex
	// Links generated using Track() are cached here so as to not query
	// the database for the link UUID for every message sent. This has to
	// be locked as it may be used externally when previewing campaigns.
	links    map[string]string
	linksMut sync.RWMutex

	subFetchQueue      chan *models.Campaign
	campMsgQueue       chan CampaignMessage
	logsChan           chan models.MessengerLog
	campMsgErrorQueue  chan msgError
	campMsgErrorCounts map[int]int
	msgQueue           chan Message

	// Sliding window keeps track of the total number of messages sent in a period
	// and on reaching the specified limit, waits until the window is over before
	// sending further messages.
	slidingWindowNumMsg int
	slidingWindowStart  time.Time

	tplFuncs template.FuncMap

	publisher        utils.NatsPublisher
	broadcastSubject string

	//Sms properties
	smsProps          models.SmsConfig
	segmentServiceUrl string
	oneAppAuthUrl     string

	gatewayProps     models.GatewayConfig
	gatwayConfigured map[string]bool

	gatewayContract    map[string]*models.GateWayDetails
	gatewayMut         sync.RWMutex
	freqCapRedisScript string
}

// CampaignMessage represents an instance of campaign message to be pushed out,
// specific to a subscriber, via the campaign's messenger.
type CampaignMessage struct {
	Campaign   *models.Campaign
	Subscriber models.Subscriber
	Cache      models.Cache
	RealTime   map[string]string
	File       map[string]string
	Constant   map[string]interface{}

	MobileNumber string
	SmsText      string

	//added for sms campaign support
	RequestParams  string
	Url            string
	RequestHeaders string
	RequestBody    string
	Method         string

	Tx           map[string]interface{}
	from         string
	to           string
	subject      string
	body         []byte
	altBody      []byte
	unsubURL     string
	GatewayId    string
	Gt           *models.GateWayDetails
	Gateway      map[string]interface{}
	Target       string
	CacheDetails map[string]string
	Ctx          context.Context
	MsgId        string
	Data         map[string]string
	Auth         map[string]string
}

// Message represents a generic message to be pushed to a messenger.
type Message struct {
	messenger.Message
	Subscriber models.Subscriber

	// Messenger is the messenger backend to use: email|postback.
	Messenger        string
	Label            string
	ProcessId        string
	DeDuplication    bool
	DuplicationLevel string
	ProcessDuration  int
}

// Config has parameters for configuring the manager.
type Config struct {
	// Number of subscribers to pull from the DB in a single iteration.
	BatchSize             int
	Concurrency           int
	MessageRate           int
	MaxSendErrors         int
	SlidingWindow         bool
	SlidingWindowDuration time.Duration
	SlidingWindowRate     int
	RequeueOnError        bool
	FromEmail             string
	IndividualTracking    bool
	LinkTrackURL          string
	UnsubURL              string
	OptinURL              string
	MessageURL            string
	ViewTrackURL          string
	ArchiveURL            string
	UnsubHeader           bool
	// Interval to scan the DB for active campaign checkpoints.
	ScanInterval time.Duration

	// ScanCampaigns indicates whether this instance of manager will scan the DB
	// for active campaigns and process them.
	// This can be used to run multiple instances of listmonk
	// (exposed to the internet, private etc.) where only one does campaign
	// processing while the others handle other kinds of traffic.
	ScanCampaigns bool
}

type msgError struct {
	camp *models.Campaign
	err  error
}

var pushTimeout = time.Second * 3

func (m *Manager) SendMessengerLogs(log models.MessengerLog) {
	m.logsChan <- log
}

// New returns a new instance of Mailer.
func New(cfg Config, store Store, notifCB models.AdminNotifCallback, i *i18n.I18n, l *log.Logger, segUrl string, oneAppUrl string) *Manager {
	if cfg.BatchSize < 1 {
		cfg.BatchSize = 1000
	}
	if cfg.Concurrency < 1 {
		cfg.Concurrency = 1
	}
	if cfg.MessageRate < 1 {
		cfg.MessageRate = 1
	}

	log.Printf("manager: application manager configured with %v concurrency ", cfg.Concurrency)

	m := &Manager{
		cfg:                cfg,
		store:              store,
		i18n:               i,
		notifCB:            notifCB,
		logger:             l,
		messengers:         make(map[string]messenger.Messenger),
		camps:              make(map[int]*models.Campaign),
		campRates:          make(map[int]*ratecounter.RateCounter),
		tpls:               make(map[int]*models.Template),
		links:              make(map[string]string),
		subFetchQueue:      make(chan *models.Campaign, cfg.Concurrency),
		campMsgQueue:       make(chan CampaignMessage, cfg.Concurrency*cfg.MessageRate*2),
		msgQueue:           make(chan Message, cfg.Concurrency*cfg.MessageRate*2),
		logsChan:           make(chan models.MessengerLog, cfg.BatchSize*2),
		campMsgErrorQueue:  make(chan msgError, cfg.MaxSendErrors),
		campMsgErrorCounts: make(map[int]int),
		slidingWindowStart: time.Now(),
		campaignMap:        make(map[int]*models.Campaign),
		campSmsMap:         make(map[int]map[int]int),
		segmentServiceUrl:  segUrl,
		oneAppAuthUrl:      oneAppUrl,
		categoryMap:        make(map[int]models.Preference),
		gatewayContract:    make(map[string]*models.GateWayDetails),
	}
	m.tplFuncs = m.makeGnericFuncMap()

	return m
}

// NewCampaignMessage creates and returns a CampaignMessage that is made available
// to message templates while they're compiled. It represents a message from
// a campaign that's bound to a single Subscriber.
func (m *Manager) NewCampaignMessage(c *models.Campaign, s models.Subscriber, ctx context.Context, msgId string) (CampaignMessage, error) {
	//Added by Deepali
	logger := ctx.Value(tracer.TRACE_LOGGER_KEY).(push.Logger)

	logger.Info().Msgf("Starting messaging at channel: %v", c.Messenger)
	var cache models.Cache

	msg := CampaignMessage{
		Campaign:     c,
		Subscriber:   s,
		Cache:        cache,
		subject:      c.Subject,
		from:         c.FromEmail,
		to:           "",
		unsubURL:     fmt.Sprintf(m.cfg.UnsubURL, c.UUID, s.UUID),
		File:         make(map[string]string),
		Tx:           make(map[string]interface{}),
		Target:       "",
		CacheDetails: make(map[string]string),
		Constant:     c.Constant,
		Ctx:          ctx,
		MsgId:        msgId,
		Data:         make(map[string]string),
		Gt:           c.Gateway,
		GatewayId:    c.GatewayId,
		Auth:         make(map[string]string),
	}

	msg.File = s.FileAttribs

	if c.IsCachePresent {
		logger.Info().Msg("Cache attribute is present")
		cache = external.GetCacheTerminalData(s.Name, ctx)

		if cache.TerminalId == "" {
			return msg, fmt.Errorf("terminalId %v not found in cache and via api", s.Name)
		}
		msg.Cache = cache
	}

	if c.IsRealTimePresent {
		logger.Info().Msg("RealTime attribute is present")
		var properties []string

		if len(c.RealTimeAttribs) == 0 {
			c.RealTimeAttribsMut.Lock()
			if len(c.RealTimeAttribs) == 0 {
				logger.Info().Msg("Fetching realtime attribute list")
				response := external.GetRealTimeSegmentProperties(m.segmentServiceUrl, "Real Time Segment", ctx)
				for _, r := range response {
					searchString := ".RealTime." + r
					needToFetchProperty := strings.Contains(c.AltBody.String, searchString) ||
						strings.Contains(c.Subject, searchString) ||
						strings.Contains(string(c.FcmKeyval), searchString) ||
						strings.Contains(c.TemplateBody, searchString) ||
						strings.Contains(c.Body, searchString)
					if needToFetchProperty {
						logger.Info().Msgf("Added property %v", r)
						properties = append(properties, r)
					}
				}
				c.RealTimeAttribs = properties
			}
			c.RealTimeAttribsMut.Unlock()
		} else {
			properties = c.RealTimeAttribs
		}

		msg.RealTime = make(map[string]string)
		for _, r := range properties {
			logger.Info().Msgf("Fetching realtime data %v for %v", s.Name, r)
			value := external.GetRealTimeSegmentData(r, s.Name, m.segmentServiceUrl, ctx)
			logger.Info().Msgf("Fetched realtime data %v value %v for %v", s.Name, value, r)
			msg.RealTime[r] = value
		}
	}

	keyVal := make(map[string]interface{})

	if c.Messenger == "email" {
		if s.Type != "customer" && s.Type != "" {
			if len(cache.TerminalId) == 0 {
				cache = external.GetCacheTerminalData(s.Name, ctx)
				if cache.TerminalId == "" {
					return msg, fmt.Errorf("terminalId %v not found in cache and via api", s.Name)
				}
				msg.Cache = cache
			}

			s.Email = cache.Email
			if cache.Email != "" {
				email := utils.Decryptdata(cache.Email, logger)
				if email != "" {
					msg.to = email
					keyVal["Target"] = email
					msg.Target = email
				}
			}
		} else {
			msg.to = s.Email
			keyVal["Target"] = s.Email
			msg.Target = s.Email
		}

	} else if c.Messenger == "sms" || c.Messenger == "whatsapp" {

		msg.MobileNumber = s.Name
		keyVal["Name"] = s.Name

		if s.Type != "customer" && s.Type != "" {
			if len(cache.TerminalId) == 0 {
				cache = external.GetCacheTerminalData(s.Name, ctx)
				if cache.TerminalId == "" {
					return msg, fmt.Errorf("terminalId %v not found in cache and via api", s.Name)
				}
				msg.Cache = cache
			}
			if cache.MobileNumber != "" {
				mobile := utils.Decryptdata(cache.MobileNumber, logger)
				if mobile != "" {
					msg.to = mobile
					msg.MobileNumber = mobile
					keyVal["Target"] = mobile
					msg.Target = mobile
					msg.CacheDetails["mobileNumber"] = mobile
					msg.CacheDetails["terminalId"] = cache.TerminalId
					msg.CacheDetails["dbaName"] = cache.DbaName
					msg.CacheDetails["corporateId"] = cache.CorporateId
					msg.CacheDetails["merchantId"] = cache.MerchantId
				} else {
					return msg, fmt.Errorf("no mobile number found for subscriber while running campaign %v", c.ID)
				}
			}
		} else {
			msg.to = s.Name
			keyVal["Target"] = s.Name
			msg.Target = s.Name
		}
	}

	msg.Tx["Subscriber"] = keyVal

	if msg.Gt != nil {
		msg.Gateway = models.GetGatewayProps()
		if msg.Gt.AuthConfigInterface != nil {
			msg.Auth = msg.Gt.AuthConfigInterface.GetValue()
		}
	}

	if err := msg.render(); err != nil {
		return msg, err
	}
	return msg, nil
	//Ended by Deepali
}

// AddMessenger adds a Messenger messaging backend to the manager.
func (m *Manager) AddMessenger(msg messenger.Messenger) error {
	id := msg.Name()
	if _, ok := m.messengers[id]; ok {
		return fmt.Errorf("messenger '%s' is already loaded", id)
	}
	m.messengers[id] = msg
	return nil
}

func (m *Manager) AddGatewatProps(s models.GatewayConfig) {
	m.gatewayProps = s
}

func (m *Manager) AddGatewayFlags(s map[string]bool) {
	m.gatwayConfigured = s
}

func (m *Manager) AddGatewayFlag(s string) {
	m.gatwayConfigured[s] = true
}

func (m *Manager) GetGatewayFlag(s string) bool {
	return m.gatwayConfigured[s]
}

func (m *Manager) AddPublisher(p utils.NatsPublisher) error {
	m.publisher = p
	return nil
}

func (m *Manager) AddDb(db *sqlx.DB) {
	m.db = db
}

func (m *Manager) AddFreqCapRedisScript(script string) {
	m.freqCapRedisScript = script
}

// PushMessage pushes an arbitrary non-campaign Message to be sent out by the workers.
// It times out if the queue is busy.
func (m *Manager) PushMessage(msg Message) error {
	t := time.NewTicker(pushTimeout)
	defer t.Stop()

	select {
	case m.msgQueue <- msg:
	case <-t.C:
		m.logger.Printf("message push timed out: '%s'", msg.Subject)
		return errors.New("message push timed out")
	}
	return nil
}

// PushCampaignMessage pushes a campaign messages into a queue to be sent out by the workers.
// It times out if the queue is busy.
func (m *Manager) PushCampaignMessage(msg CampaignMessage) error {
	t := time.NewTicker(pushTimeout)
	logger := msg.Ctx.Value(tracer.TRACE_LOGGER_KEY).(push.Logger)
	defer t.Stop()

	select {
	case m.campMsgQueue <- msg:
	case <-t.C:
		logger.Warn().Msgf("message push timed out: '%s'", msg.Subject())
		return errors.New("message push timed out")
	}
	return nil
}

// HasMessenger checks if a given messenger is registered.
func (m *Manager) HasMessenger(id string) bool {
	_, ok := m.messengers[id]
	return ok
}

// HasRunningCampaigns checks if there are any active campaigns.
func (m *Manager) HasRunningCampaigns() bool {
	m.campsMut.Lock()
	defer m.campsMut.Unlock()
	return len(m.camps) > 0
}

// GetCampaignStats returns campaign statistics.
func (m *Manager) GetCampaignStats(id int) CampStats {
	n := 0

	m.campsMut.Lock()
	if r, ok := m.campRates[id]; ok {
		n = int(r.Rate())
	}
	m.campsMut.Unlock()

	return CampStats{SendRate: n}
}

// Added by Sachin
func (m *Manager) GetCampaignStatsV2(id int) CampStats {
	n := 0

	value := utils.UpdateCounter(strconv.Itoa(id)+"_"+"listmonk"+"_"+"counter_perminute", 0)

	if value == 1 {
		utils.AddedExpiration(strconv.Itoa(id)+"_"+"listmonk"+"_"+"counter_perminute", 60)
	}
	n = int(value)
	return CampStats{SendRate: n}
}

// CacheTpl caches a template for ad-hoc use. This is currently only used by tx templates.
func (m *Manager) CacheTpl(id int, tpl *models.Template) {
	m.tplsMut.Lock()
	m.tpls[id] = tpl
	m.tplsMut.Unlock()
}

func (m *Manager) CacheGatewayContract(messenger string, gtd *models.GateWayDetails) {
	m.gatewayMut.Lock()
	defer m.gatewayMut.Unlock()
	gt, has := m.gatewayContract[fmt.Sprintf("%s_%d", messenger, gtd.ID)]

	if has && gt.AuthConfigInterface != nil {
		gt.AuthConfigInterface.Close()
	}

	m.gatewayContract[fmt.Sprintf("%s_%d", messenger, gtd.ID)] = gtd

	if gtd.IsDefault {
		m.gatewayContract[fmt.Sprintf("%s_%s", messenger, "default")] = gtd
	}
}

// DeleteTpl deletes a cached template.
func (m *Manager) DeleteTpl(id int) {
	m.tplsMut.Lock()
	delete(m.tpls, id)
	m.tplsMut.Unlock()
}

// GetTpl returns a cached template.
func (m *Manager) GetTpl(id int) (*models.Template, error) {
	m.tplsMut.RLock()
	tpl, ok := m.tpls[id]
	m.tplsMut.RUnlock()

	if !ok {
		out, err := m.GetSingleTemplate(id)

		if err != nil {
			return nil, fmt.Errorf("template %d not found", id)
		} else {
			return out, nil
		}

	}

	return tpl, nil
}

// GetGateway returns a cached provider.
func (m *Manager) GetGateway(key string) (*models.GateWayDetails, error) {
	m.gatewayMut.RLock()
	msgDetails, ok := m.gatewayContract[key]
	defer m.gatewayMut.RUnlock()

	if !ok {
		return nil, nil
	}
	return msgDetails, nil
}

// get template from db if not present
func (m *Manager) GetSingleTemplate(id int) (*models.Template, error) {

	tpl, err := m.store.GetTemplateFromDb(id)

	if err == nil {

		if err := tpl.Compile(m.GenericTemplateFuncs()); err != nil {
			return nil, err
		}
		m.CacheTpl(id, tpl)
		return tpl, nil
	}
	return nil, err

}

func (m *Manager) GetSingleGateway(id int, logger push.Logger) (*models.GateWayDetails, error) {

	gtd, err := m.store.GetGatewayFromDb(id)

	if err != nil {
		return nil, err
	}

	if !gtd.IsDefault {
		c := m.store.GetMessengerCount(gtd.Messenger.String)
		if c == 1 {
			gtd.IsDefault = true
		}
	}

	if gtd.Method.Valid && gtd.Method.String != "smtp" {

		if err := gtd.CompileV2(m.GenericTemplateFuncs()); err != nil {
			logger.Error().Msgf("error compiling gateway details with %d: %v", gtd.ID, err)
			return nil, err
		}
	}

	if gtd.AuthConfig != nil && gtd.AuthConfig.Url != "" {
		gtd.AuthConfigInterface = authprovider.NewGatewayAuthProvider(*gtd.AuthConfig)
	}
	m.CacheGatewayContract(gtd.Messenger.String, gtd)
	m.AddGatewayFlag(gtd.Messenger.String)
	logger.Info().Msgf("gateway details == %v", gtd)

	return gtd, nil
}

// worker is a blocking function that perpetually listents to events (message) on different
// queues and processes them.
func (m *Manager) worker() {
	// Counter to keep track of the message / sec rate limit.
	numMsg := 0
	for {
		select {
		// Campaign message.
		case msg, ok := <-m.campMsgQueue:
			if !ok {
				return
			}

			// Pause on hitting the message rate.
			if numMsg >= m.cfg.MessageRate {
				time.Sleep(time.Second)
				numMsg = 0
			}
			numMsg++

			// Outgoing message.
			out := messenger.Message{
				From:        msg.from,
				To:          []string{msg.to},
				Subject:     msg.subject,
				ContentType: msg.Campaign.ContentType,
				Body:        msg.body,
				AltBody:     msg.altBody,
				Subscriber:  msg.Subscriber,
				Campaign:    msg.Campaign,
			}

			h := textproto.MIMEHeader{}
			h.Set(models.EmailHeaderCampaignUUID, msg.Campaign.UUID)
			h.Set(models.EmailHeaderSubscriberUUID, msg.Subscriber.UUID)

			// Attach List-Unsubscribe headers?
			if m.cfg.UnsubHeader {
				h.Set("List-Unsubscribe-Post", "List-Unsubscribe=One-Click")
				h.Set("List-Unsubscribe", `<`+msg.unsubURL+`>`)
			}

			// Attach any custom headers.
			if len(msg.Campaign.Headers) > 0 {
				for _, set := range msg.Campaign.Headers {
					for hdr, val := range set {
						h.Add(hdr, val)
					}
				}
			}

			out.Headers = h

			if _, err := m.messengers[msg.Campaign.Messenger].Push(out); err != nil {
				m.logger.Printf("error sending message in campaign %s: subscriber %s: %v",
					msg.Campaign.Name, msg.Subscriber.UUID, err)

				select {
				case m.campMsgErrorQueue <- msgError{camp: msg.Campaign, err: err}:
				default:
					continue
				}
			}

			m.campsMut.Lock()
			if r, ok := m.campRates[msg.Campaign.ID]; ok {
				r.Incr(1)
			}
			m.campsMut.Unlock()

		// Arbitrary message.
		case msg, ok := <-m.msgQueue:
			if !ok {
				return
			}

			_, err := m.messengers[msg.Messenger].Push(messenger.Message{
				From:             msg.From,
				To:               msg.To,
				Subject:          msg.Subject,
				ContentType:      msg.ContentType,
				Body:             msg.Body,
				AltBody:          msg.AltBody,
				Subscriber:       msg.Subscriber,
				Campaign:         msg.Campaign,
				AdditionalValues: msg.AdditionalValues,
			})
			if err != nil {
				m.logger.Printf("error sending message '%s': %v", msg.Subject, err)
			}
		}
	}
}

// TemplateFuncs returns the template functions to be applied into
// compiled campaign templates.
func (m *Manager) TemplateFuncs(c *models.Campaign) template.FuncMap {
	f := template.FuncMap{
		"TrackLink": func(url string, msg *CampaignMessage) string {
			subUUID := msg.Subscriber.UUID
			if !m.cfg.IndividualTracking {
				subUUID = dummyUUID
			}

			return m.trackLink(url, msg.Campaign.UUID, subUUID)
		},
		"TrackView": func(msg *CampaignMessage) template.HTML {
			subUUID := msg.Subscriber.UUID
			if !m.cfg.IndividualTracking {
				subUUID = dummyUUID
			}

			return template.HTML(fmt.Sprintf(`<img src="%s" alt="" />`,
				fmt.Sprintf(m.cfg.ViewTrackURL, msg.Campaign.UUID, subUUID)))
		},
		"UnsubscribeURL": func(msg *CampaignMessage) string {
			return msg.unsubURL
		},
		"ManageURL": func(msg *CampaignMessage) string {
			return msg.unsubURL + "?manage=true"
		},
		"OptinURL": func(msg *CampaignMessage) string {
			// Add list IDs.
			// TODO: Show private lists list on optin e-mail
			return fmt.Sprintf(m.cfg.OptinURL, msg.Subscriber.UUID, "")
		},
		"MessageURL": func(msg *CampaignMessage) string {
			return fmt.Sprintf(m.cfg.MessageURL, c.UUID, msg.Subscriber.UUID)
		},
		"ArchiveURL": func() string {
			return m.cfg.ArchiveURL
		},
	}

	for k, v := range m.tplFuncs {
		f[k] = v
	}

	return f
}

func (m *Manager) GenericTemplateFuncs() template.FuncMap {
	return m.tplFuncs
}

// Close closes and exits the campaign manager.
func (m *Manager) Close() {
	close(m.subFetchQueue)
	close(m.campMsgErrorQueue)
	close(m.msgQueue)
}

// scanCampaigns is a blocking function that periodically scans the data source
// for campaigns to process and dispatches them to the manager.
func (m *Manager) scanCampaigns(tick time.Duration) {
	t := time.NewTicker(tick)
	defer t.Stop()

	for {
		select {
		// Periodically scan the data source for campaigns to process.
		case <-t.C:
			campaigns, err := m.store.NextCampaigns(m.getPendingCampaignIDs())
			if err != nil {
				m.logger.Printf("error fetching campaigns: %v", err)
				continue
			}

			for _, c := range campaigns {
				if err := m.addCampaign(c); err != nil {
					m.logger.Printf("error processing campaign (%s): %v", c.Name, err)
					continue
				}
				m.logger.Printf("start processing campaign (%s)", c.Name)

				// If subscriber processing is busy, move on. Blocking and waiting
				// can end up in a race condition where the waiting campaign's
				// state in the data source has changed.
				select {
				case m.subFetchQueue <- c:
				default:
				}
			}

			// Aggregate errors from sending messages to check against the error threshold
			// after which a campaign is paused.
		case e, ok := <-m.campMsgErrorQueue:
			if !ok {
				return
			}
			if m.cfg.MaxSendErrors < 1 {
				continue
			}

			// If the error threshold is met, pause the campaign.
			m.campMsgErrorCounts[e.camp.ID]++
			if m.campMsgErrorCounts[e.camp.ID] >= m.cfg.MaxSendErrors {
				m.logger.Printf("error counted exceeded %d. pausing campaign %s",
					m.cfg.MaxSendErrors, e.camp.Name)

				if m.isCampaignProcessing(e.camp.ID) {
					m.exhaustCampaign(e.camp, models.CampaignStatusPaused)
				}
				delete(m.campMsgErrorCounts, e.camp.ID)

				// Notify admins.
				m.sendNotif(e.camp, models.CampaignStatusPaused, "Too many errors", tracer.GenerateNewTracingContext(context.Background(), "deprecated", ""))
			}
		}
	}
}

// addCampaign adds a campaign to the process queue.
func (m *Manager) addCampaign(c *models.Campaign) error {
	// Validate messenger.
	if _, ok := m.messengers[c.Messenger]; !ok {
		m.store.UpdateCampaignStatus(c.ID, models.CampaignStatusCancelled, null.StringFrom("system"))
		return fmt.Errorf("unknown messenger %s on campaign %s", c.Messenger, c.Name)
	}

	escapeContent := false
	if m.GetGatewayFlag(c.Messenger) {
		gtd, err := m.GetGateway(fmt.Sprintf("%s_default", c.Messenger))
		if err != nil {
			return err
		}
		if gtd.Method.Valid && gtd.Method.String == "get" {
			escapeContent = true
		} else if gtd.RequestParamTpl != nil {
			escapeContent = true
		}
	}

	// Load the template.
	if err := c.CompileTemplate(m.TemplateFuncs(c), escapeContent); err != nil {
		return err
	}

	// Add the campaign to the active map.
	m.campsMut.Lock()
	m.camps[c.ID] = c
	m.campRates[c.ID] = ratecounter.NewRateCounter(time.Minute)
	m.campsMut.Unlock()
	return nil
}

// getPendingCampaignIDs returns the IDs of campaigns currently being processed.
func (m *Manager) getPendingCampaignIDs() []int64 {
	// Needs to return an empty slice in case there are no campaigns.
	m.campsMut.RLock()
	ids := make([]int64, 0, len(m.camps))
	for _, c := range m.camps {
		ids = append(ids, int64(c.ID))
	}
	m.campsMut.RUnlock()
	return ids
}

func (m *Manager) PauseCampaign(campaignId int, isPaused bool, updatedBy null.String) error {
	err := m.store.PauseCampaign(campaignId, isPaused, updatedBy)
	return err
}

// nextSubscribers processes the next batch of subscribers in a given campaign.
// It returns a bool indicating whether any subscribers were processed
// in the current batch or not. A false indicates that all subscribers
// have been processed, or that a campaign has been paused or cancelled.
func (m *Manager) nextSubscribers(c *models.Campaign, batchSize int) (bool, error) {
	// Fetch a batch of subscribers.
	subs, err := m.store.NextSubscribers(c.ID, batchSize, c.Category.Int, c.Messenger, c.DefaultPref)
	if err != nil {
		return false, fmt.Errorf("error fetching campaign subscribers (%s): %v", c.Name, err)
	}

	// There are no subscribers.
	if len(subs) == 0 {
		return false, nil
	}

	// Is there a sliding window limit configured?
	hasSliding := m.cfg.SlidingWindow &&
		m.cfg.SlidingWindowRate > 0 &&
		m.cfg.SlidingWindowDuration.Seconds() > 1

	// Push messages.
	for _, s := range subs {
		// Send the message.
		msg, err := m.NewCampaignMessage(c, s, tracer.GenerateNewTracingContext(context.Background(), "consumer", ""), "")
		if err != nil {
			m.logger.Printf("error rendering message (%s) (%s): %v", c.Name, s.Email, err)
			continue
		}

		// Push the message to the queue while blocking and waiting until
		// the queue is drained.
		m.campMsgQueue <- msg

		// Check if the sliding window is active.
		if hasSliding {
			diff := time.Now().Sub(m.slidingWindowStart)

			// Window has expired. Reset the clock.
			if diff >= m.cfg.SlidingWindowDuration {
				m.slidingWindowStart = time.Now()
				m.slidingWindowNumMsg = 0
				continue
			}

			// Have the messages exceeded the limit?
			m.slidingWindowNumMsg++
			if m.slidingWindowNumMsg >= m.cfg.SlidingWindowRate {
				wait := m.cfg.SlidingWindowDuration - diff

				m.logger.Printf("messages exceeded (%d) for the window (%v since %s). Sleeping for %s.",
					m.slidingWindowNumMsg,
					m.cfg.SlidingWindowDuration,
					m.slidingWindowStart.Format(time.RFC822Z),
					wait.Round(time.Second)*1)

				m.slidingWindowNumMsg = 0
				time.Sleep(wait)
			}
		}
	}

	return true, nil
}

// isCampaignProcessing checks if the campaign is being processed.
func (m *Manager) isCampaignProcessing(id int) bool {
	m.campsMut.RLock()
	_, ok := m.camps[id]
	m.campsMut.RUnlock()
	return ok
}

func (m *Manager) exhaustCampaign(c *models.Campaign, status string) (*models.Campaign, error) {
	m.campsMut.Lock()
	delete(m.camps, c.ID)
	delete(m.campRates, c.ID)
	m.campsMut.Unlock()

	// A status has been passed. Change the campaign's status
	// without further checks.
	if status != "" {
		if err := m.store.UpdateCampaignStatus(c.ID, status, null.StringFrom("system")); err != nil {
			m.logger.Printf("error updating campaign (%s) status to %s: %v", c.Name, status, err)
		} else {
			m.logger.Printf("set campaign (%s) to %s", c.Name, status)
		}
		return c, nil
	}

	// Fetch the up-to-date campaign status from the source.
	cm, err := m.store.GetCampaign(c.ID)
	if err != nil {
		return nil, err
	}

	// If a running campaign has exhausted subscribers, it's finished.
	if cm.Status == models.CampaignStatusRunning {
		cm.Status = models.CampaignStatusFinished
		if err := m.store.UpdateCampaignStatus(c.ID, models.CampaignStatusFinished, null.StringFrom("system")); err != nil {
			m.logger.Printf("error finishing campaign (%s): %v", c.Name, err)
		} else {
			m.logger.Printf("campaign (%s) finished", c.Name)
			// m.logger.Printf("deleting the targets")
			// if er := m.store.DeleteTargets(c.ID); er != nil {
			// 	m.logger.Printf("error deleting targets from campaign targets for id: %d", c.ID)
			// }
		}
	} else {
		m.logger.Printf("stop processing campaign (%s)", c.Name)
	}

	return cm, nil
}

// trackLink register a URL and return its UUID to be used in message templates
// for tracking links.
func (m *Manager) trackLink(url, campUUID, subUUID string) string {
	url = strings.ReplaceAll(url, "&amp;", "&")

	m.linksMut.RLock()
	if uu, ok := m.links[url]; ok {
		m.linksMut.RUnlock()
		return fmt.Sprintf(m.cfg.LinkTrackURL, uu, campUUID, subUUID)
	}
	m.linksMut.RUnlock()

	// Register link.
	uu, err := m.store.CreateLink(url)
	if err != nil {
		m.logger.Printf("error registering tracking for link '%s': %v", url, err)

		// If the registration fails, fail over to the original URL.
		return url
	}

	m.linksMut.Lock()
	m.links[url] = uu
	m.linksMut.Unlock()

	return fmt.Sprintf(m.cfg.LinkTrackURL, uu, campUUID, subUUID)
}

// sendNotif sends a notification to registered admin e-mails.
func (m *Manager) sendNotif(c *models.Campaign, status, reason string, ctx context.Context) error {
	var (
		subject = fmt.Sprintf("%s: %s", strings.Title(status), c.Name)
		data    = map[string]interface{}{
			"ID":     c.ID,
			"Name":   c.Name,
			"Status": status,
			"Sent":   c.Sent,
			"ToSend": c.ToSend,
			"Reason": reason,
		}
	)
	return m.notifCB(subject, data, ctx)
}

// render takes a Message, executes its pre-compiled Campaign.Tpl
// and applies the resultant bytes to Message.body to be used in messages.
func (m *CampaignMessage) render() error {
	out := bytes.Buffer{}

	// Render the subject if it's a template.
	if m.Campaign.SubjectTpl != nil {
		if err := m.Campaign.SubjectTpl.ExecuteTemplate(&out, models.ContentTpl, m); err != nil {
			return err
		}
		m.subject = out.String()
		out.Reset()
	}

	if m.Campaign.Messenger == "email" {
		if err := m.Campaign.Tpl.ExecuteTemplate(&out, models.BaseTpl, m); err != nil {
			return err
		}
		m.body = out.Bytes()

	} else {
		if err := m.Campaign.NonEmailTpl.ExecuteTemplate(&out, models.BaseTpl, m); err != nil {
			return err
		}
		m.body = out.Bytes()
	}

	// Compile the main template.

	// Is there an alt body?
	if m.Campaign.ContentType != models.CampaignContentTypePlain && m.Campaign.AltBody.Valid {
		if m.Campaign.AltBodyTpl != nil {
			b := bytes.Buffer{}
			if err := m.Campaign.AltBodyTpl.ExecuteTemplate(&b, models.ContentTpl, m); err != nil {
				return err
			}
			m.altBody = b.Bytes()
		} else {
			m.altBody = []byte(m.Campaign.AltBody.String)
		}
	}

	if m.Campaign.KeyValueTpl != nil {
		b := bytes.Buffer{}
		if err := m.Campaign.KeyValueTpl.ExecuteTemplate(&b, BaseTPL, m); err != nil {
			return err
		}

		var data []map[string]string
		err := json.Unmarshal(b.Bytes(), &data)

		if err != nil {
			return nil
		}

		result := make(map[string]string)

		for _, item := range data {
			key, ok := item["key"]
			if !ok {
				continue
			}
			value, ok := item["value"]
			if !ok {
				continue
			}
			result[key] = value
		}
		m.Data = result

		b.Reset()

	} else {
		m.Data = m.Campaign.Data
	}

	if m.Gt != nil {
		b := bytes.Buffer{}

		if m.Gt.Method.String != "smtp" && len(m.body) > 0 {
			m.Tx["Content"] = models.QuoteString(string(m.body))
		}

		method := "get"

		if m.Gt.Method.Valid {
			method = m.Gt.Method.String
		}

		if m.Gt.Url.Valid {
			m.Url = m.Gt.Url.String
		}
		m.Method = method
		if m.Gt.RequestBodyTpl != nil {
			if err := m.Gt.RequestBodyTpl.ExecuteTemplate(&b, BaseTPL, m); err != nil {
				return err
			}
			m.RequestBody = b.String()
			b.Reset()
		} else {
			m.RequestBody = m.Gt.RequestBody.String
		}
		if m.Gt.RequestParamTpl != nil {
			if err := m.Gt.RequestParamTpl.ExecuteTemplate(&b, BaseTPL, m); err != nil {
				return err
			}
			m.RequestParams = b.String()
			b.Reset()
		} else {
			m.RequestParams = m.Gt.RequestParams.String
		}
		if m.Gt.HeaderTpl != nil {
			if err := m.Gt.HeaderTpl.ExecuteTemplate(&b, BaseTPL, m); err != nil {
				return err
			}
			m.RequestHeaders = b.String()
			b.Reset()
		} else {
			m.RequestHeaders = m.Gt.Headers.String
		}
	}

	return nil
}

// Subject returns a copy of the message subject
func (m *CampaignMessage) Subject() string {
	return m.subject
}

// Body returns a copy of the message body.
func (m *CampaignMessage) Body() []byte {
	out := make([]byte, len(m.body))
	copy(out, m.body)
	return out
}

// AltBody returns a copy of the message's alt body.
func (m *CampaignMessage) AltBody() []byte {
	out := make([]byte, len(m.altBody))
	copy(out, m.altBody)
	return out
}

func (m *Manager) makeGnericFuncMap() template.FuncMap {
	f := template.FuncMap{
		"Date": func(layout string) string {
			if layout == "" {
				layout = time.ANSIC
			}
			return time.Now().Format(layout)
		},
		"L": func() *i18n.I18n {
			return m.i18n
		},
		"Safe": func(safeHTML string) template.HTML {
			str := template.HTML(safeHTML)
			return str
		},
		"objectListT": objectListT,
		"DelayDate": func(layout string) string {
			if layout == "" {
				layout = time.ANSIC
			}
			return time.Now().Add(time.Duration(2) * time.Millisecond).Format(layout)
		},
	}

	for k, v := range sprig.GenericFuncMap() {
		f[k] = v
	}

	return f
}

// Added  by Sachin
func (m *Manager) LockCampMap() {
	m.campsMut.Lock()
}

func (m *Manager) UnLockCampMap() {
	m.campsMut.Unlock()
}

func (m *Manager) AddCampaignMap(c *models.Campaign) {

	m.campMapMut.Lock()
	defer m.campMapMut.Unlock()
	_, b := m.campaignMap[c.ID]

	if !b {
		m.addCampaignToMap(c)
	}

}

func (m *Manager) GetCampaignFromId(id int) (*models.Campaign, error) {
	defer m.campMapMut.Unlock()

	m.campMapMut.Lock()

	c, has := m.campaignMap[id]

	if !has {
		campaign, h := m.GetActiveCampaign(id)

		if !h {
			return nil, fmt.Errorf("campaign not found")
		}
		campaign.Label = sanitizeString(campaign.Name)
		campaign.DefaultPref = mustSend(campaign.PrefString)

		err := m.addCampaignToMap(campaign)
		if err != nil {
			return nil, err
		}
		cam := m.campaignMap[id]
		return cam, nil

	}

	return c, nil
}

func mustSend(s string) bool {
	lower := strings.ToLower(s)
	switch lower {
	case "false":
		return false
	default:
		return true
	}
}

func sanitizeString(input string) string {
	input = strings.TrimSpace(input)
	if len(input) > 40 {
		input = input[:40]
	}
	re, err := regexp.Compile(`[^a-zA-Z0-9]+`)
	if err != nil {
		return "invalid_camp_name"
	}
	input = re.ReplaceAllString(input, "_")
	input = strings.ToLower(input)
	return input
}

func (m *Manager) FetchCampaignFromMap(id int) *models.Campaign {

	m.campMapMut.Lock()
	defer m.campMapMut.Unlock()
	value := m.campaignMap[id]
	return value
}

func (m *Manager) FetchSubscriber(c int, batchSize int, catId int, channel string, prefDefault bool) ([]models.Subscriber, error) {
	// Fetch a batch of subscribers.
	return m.store.NextSubscribers(c, batchSize, catId, channel, prefDefault)
}

func (m *Manager) UpdateToSend(toSend int, campId int) error {
	// Update to send.
	return m.store.UpdateToSend(toSend, campId)
}

func (m *Manager) UpdateSentStatus(sent int, campId int) error {
	// Update sent.
	return m.store.UpdateSentStatus(sent, campId)
}

func (m *Manager) GetBatchSize() int {
	return m.cfg.BatchSize
}

func (m *Manager) FetchCampaign(cId int) (*models.Campaign, bool) {
	start := time.Now()

	m.logger.Printf("start fetchcampaign: %v", start)
	campaigns, err := m.store.NextCampaignsV2(int64(cId))
	m.logger.Printf("end fetching campaigns: %v", time.Since(start).Milliseconds())

	if err != nil {
		m.logger.Printf("error fetching campaigns: %v", err)
		return nil, false
	}

	if len(campaigns) >= 1 {
		return campaigns[0], true
	} else {
		m.logger.Printf("campaign with %d not found", cId)
		return nil, false
	}
}

func (m *Manager) GetActiveCampaign(cId int) (*models.Campaign, bool) {
	campaigns, err := m.store.GetActiveCampaign(int64(cId))

	if err != nil {
		m.logger.Printf("error fetching campaigns: %v", err)
		return nil, false
	}

	if len(campaigns) >= 1 {
		return campaigns[0], true
	} else {
		m.logger.Printf("No active campaign with %d not found", cId)
		return nil, false
	}
}

func (m *Manager) ExhaustCampaignV2(c *models.Campaign, status string, ctx context.Context) (*models.Campaign, error) {

	logger := ctx.Value(tracer.TRACE_LOGGER_KEY).(push.Logger)

	if status != "" {
		if err := m.store.UpdateCampaignStatus(c.ID, status, null.StringFrom("system")); err != nil {
			logger.Error().Msgf("error updating campaign (%s) status to %s: %v", c.Name, status, err)
		} else {
			logger.Info().Msgf("set campaign (%s) to %s", c.Name, status)
		}
		return c, nil
	}

	// Fetch the up-to-date campaign status from the source.
	cm, err := m.store.GetCampaign(c.ID)
	if err != nil {
		return nil, err
	}

	// If a running campaign has exhausted subscribers, it's finished.
	if cm.Status == models.CampaignStatusRunning {
		cm.Status = models.CampaignStatusFinished
		if err := m.store.UpdateCampaignStatus(c.ID, models.CampaignStatusFinished, null.StringFrom("system")); err != nil {
			logger.Error().Msgf("error finishing campaign (%s): %v", c.Name, err)
		} else {
			logger.Info().Msgf("campaign (%s) finished", c.Name)
		}
	} else {
		logger.Info().Msgf("stop processing campaign (%s)", c.Name)
	}

	return cm, nil
}

// public method to send adminNotification
func (m *Manager) SendNotif(c *models.Campaign, status, reason string, ctx context.Context) error {
	return m.sendNotif(c, status, reason, ctx)
}

// must be used when mutex is locked
// this should also render the fcm_keyval to avoid rendering for every subscriber
func (m *Manager) addCampaignToMap(c *models.Campaign) error {
	// Validate messenger.
	if _, ok := m.messengers[c.Messenger]; !ok {
		m.store.UpdateCampaignStatus(c.ID, models.CampaignStatusCancelled, null.StringFrom("system"))
		return fmt.Errorf("unknown messenger %s on campaign %s", c.Messenger, c.Name)
	}

	if c.TemplateParams != nil {
		var jsonData map[string]interface{}
		err := json.Unmarshal([]byte(c.TemplateParams), &jsonData)
		if err == nil {
			c.Constant = jsonData
		} else {
			c.Constant = make(map[string]interface{})
		}
	} else {
		c.Constant = make(map[string]interface{})
	}

	escapeContent := false

	if m.gatwayConfigured[c.Messenger] {

		key := fmt.Sprintf("%s_default", c.Messenger)

		val, has := c.Constant[c.Messenger]

		if has {
			key = fmt.Sprintf("%s_%v", c.Messenger, val)
		}

		gtd, err := m.GetGateway(key)

		if err != nil {
			return err
		}

		if gtd == nil {
			gtd, err = m.GetGateway(fmt.Sprintf("%s_default", c.Messenger))

			if err != nil {
				return err
			}
		}

		if gtd != nil {
			c.GatewayId = fmt.Sprintf("%v", gtd.ID)
			if gtd.Method.Valid && gtd.Method.String != "smtp" {
				escapeContent = true
			} else if gtd.RequestParamTpl != nil {
				escapeContent = true
			}
			c.Gateway = gtd
		}
	}

	// Load the template.
	if err := c.CompileTemplate(m.TemplateFuncs(c), escapeContent); err != nil {
		m.logger.Printf("Error occured while compiling campaing %v, error = %v", c.ID, err)
		return err
	}

	if c.Category.Valid {
		categories, err := m.store.GetCategoriesById(int(c.Category.Int))
		if err != nil {
			return err
		}
		campaignType := c.CampaignType.String
		categoryFrequencyDetails := categories[0].FrequencyDetails[campaignType]

		c.CategoryFrequencyValue = fmt.Sprintf("%v", categoryFrequencyDetails.Freq)
		c.CategoryFrequencyDuration = fmt.Sprintf("%v", categoryFrequencyDetails.FreqDays)
		c.CategoryUpdatedAt = fmt.Sprintf("%v", categories[0].UpdatedAt.Time.Unix())
		c.IgnoreCategoryFreqCap = fmt.Sprintf("%v", categoryFrequencyDetails.IgnoreFreqCap)
	}

	notificationFrequencies, err := m.store.GetNotificationFrequencyDetails()
	if err != nil {
		return err
	}
	for _, notificationFreq := range notificationFrequencies {
		if notificationFreq.Notification == c.CampaignType.String {
			c.NotificationFrequencyValue = fmt.Sprintf("%v", notificationFreq.FrequencyValue)
			c.NotificationFrequencyDuration = fmt.Sprintf("%v", notificationFreq.FrequencyDays)
			c.NotificationUpdatedAt = fmt.Sprintf("%v", notificationFreq.UpdatedAt.Time.Unix())
			c.EnableNotificationFreqCap = fmt.Sprintf("%v", notificationFreq.EnableFreqCap)
			break
		}
	}

	// if c.Messenger == "sms" {
	// 	m.AddToCampSmsMap(c.TemplateID, c.ID)
	// }
	// Add the campaign to the active map.
	m.campaignMap[c.ID] = c
	return nil
}

func (m *Manager) workerV2() {
	start := time.Now()
	numMsg := 0

	for {
		select {
		// Campaign message.
		case msg, ok := <-m.campMsgQueue:

			if !ok {
				return
			}
			logger := msg.Ctx.Value(tracer.TRACE_LOGGER_KEY).(push.Logger)
			handleRateLimit(&start, &numMsg, m.cfg.MessageRate)

			if err := m.processCampaignMessage(&msg, logger, numMsg); err != nil {
				logger.Error().Msgf("Failed to process campaign message: %v", err)
			}

		case msg, ok := <-m.msgQueue:
			if !ok {
				return
			}
			if msg.Ctx.Err() == context.Canceled || msg.Ctx.Err() == context.DeadlineExceeded {
				ctx := tracer.GenerateNewTracingContext(context.Background(), "msgQueue", "")
				msg.Ctx = ctx
			}
			logger := msg.Ctx.Value(tracer.TRACE_LOGGER_KEY).(push.Logger)
			if err := m.processTransactionMessage(msg, logger); err != nil {
				logger.Error().Msgf("error sending message '%s': %v", msg.Subject, err)
			}
		}
	}
}

func handleRateLimit(start *time.Time, numMsg *int, messageRate int) {
	if time.Since(*start).Seconds() >= 1 {
		*start = time.Now()
		*numMsg = 0
		if *numMsg >= messageRate {
			time.Sleep(time.Second)
		}
	}
	*numMsg++
}

func (m *Manager) processCampaignMessage(msg *CampaignMessage, logger push.Logger, numMsg int) error {
	var successTargets, rejectedTargets []string
	var err error

	if msg.Campaign != nil && msg.Campaign.DeDuplication {
		successTargets, rejectedTargets, err = processMessengerDedup(msg.Ctx, "campaign", strconv.Itoa(msg.Campaign.ID), msg.Campaign.FCMRoles.String, msg.Target,
			msg.Campaign.DuplicationLevel.String, false, msg.Subscriber.Name, msg.Subscriber.Type, msg.Campaign.Messenger, msg.Cache, 0)
		if err != nil {
			logger.Error().Msgf("Error during deduplication: %v", err)
		}

		for _, rejectedTarget := range rejectedTargets {
			rejectedLog := models.MessengerLog{
				RequestType:   "campaign",
				MessengerType: msg.Campaign.Messenger,
				MemberName:    msg.Subscriber.Name,
				MemberType:    (strings.ToLower(msg.Subscriber.Type)),
				Target:        rejectedTarget,
				Status:        "rejected",
				Remarks:       "rejected_due_to_duplication",
				ReferenceID:   strconv.Itoa(msg.Campaign.ID),
				CreatedBy:     "system",
			}
			m.SendMessengerLogs(rejectedLog)
		}

		if len(successTargets) == 0 {
			logger.Error().Msgf("No valid targets for campaign ID %v and subscriber ID: %v to process notification", msg.Campaign.ID, msg.Subscriber.Name)
			m.updateCounterAndCheckCompletion("failure", msg, logger)
			return nil
		}

	} else if msg.Campaign != nil && msg.Campaign.Messenger == "fcm" {
		// FCM handling logic without deduplication
		tokens := getFcmTokens(msg.Ctx, logger, msg.Subscriber.Name, msg.Campaign.FCMRoles.String)
		if len(tokens) == 0 {
			m.updateCounterAndCheckCompletion("failure", msg, logger)
			return fmt.Errorf("no FCM tokens found for member %v", msg.Subscriber.Name)
		}
		successTargets = removeDuplicatesFromFcm(tokens)
	} else if msg.Campaign != nil && msg.Campaign.Messenger == "mqtt" {
		successTargets = GetIMEIData(msg.Ctx, logger, msg.Subscriber.Name, msg.Subscriber.Type)
	} else {
		successTargets = append(successTargets, msg.Target)
	}

	if len(successTargets) > 0 {
		finalSuccessTargets := make([]string, 0)
		if msg.Campaign.EnableNotificationFreqCap == "true" {
			//Frequency capping logic
			results, err := m.applyFrequencyCapping(msg.Ctx, successTargets, msg)
			if err != nil {
				logger.Error().Msgf("Error applying frequency capping: %v", err)
				return err
			}
			for i, result := range results {
				target := successTargets[i]
				if result == 1 {
					finalSuccessTargets = append(finalSuccessTargets, target)
				} else {
					rejectedLog := m.createLogEntry(
						"campaign",
						msg.Campaign.Messenger,
						msg.Subscriber.Name,
						strings.ToLower(msg.Subscriber.Type),
						target,
						"rejected",
						"rejected_due_to_frequency_capping",
						strconv.Itoa(msg.Campaign.ID),
					)
					m.SendMessengerLogs(rejectedLog)
				}
			}
			if len(finalSuccessTargets) == 0 {
				logger.Error().Msgf("Notification limit reached for subscriber %s", msg.Subscriber.Name)
				m.updateCounterAndCheckCompletion("failure", msg, logger)
			}
		} else {
			finalSuccessTargets = successTargets
		}

		if len(finalSuccessTargets) > 0 {
			outMessage := m.createCampaignMessage(msg, finalSuccessTargets[0], finalSuccessTargets)
			m.appendHeaders(msg, &outMessage)
			err = m.sendCampaignMessage(outMessage, msg, logger, numMsg)
			if err != nil {
				return err
			}
		}
	}
	return err
}

func (m *Manager) applyFrequencyCapping(ctx context.Context, targets []string, msg *CampaignMessage) ([]int64, error) {
	if msg.Campaign.CategoryFrequencyValue != "" && msg.Campaign.NotificationFrequencyValue != "" {
		categoryID := fmt.Sprintf("%v", msg.Campaign.Category.Int)
		ignoreFreqCap := fmt.Sprintf("%v", msg.Campaign.IgnoreFrequencyCap.Bool)

		args := []interface{}{
			categoryID,
			msg.Campaign.CategoryFrequencyValue,
			msg.Campaign.CategoryUpdatedAt,
			msg.Campaign.CategoryFrequencyDuration,
			msg.Campaign.NotificationFrequencyValue,
			msg.Campaign.NotificationUpdatedAt,
			msg.Campaign.NotificationFrequencyDuration,
			ignoreFreqCap,
			msg.Campaign.IgnoreCategoryFreqCap,
			"19800",
		}

		var keys []string
		for _, target := range targets {
			baseKey := fmt.Sprintf("{%v}:%s", target, msg.Campaign.CampaignType.String)
			catKey := fmt.Sprintf("%s:%v", baseKey, categoryID)
			keys = append(keys, baseKey, catKey)
		}
		return utils.ExecuteRedisScript(ctx, m.freqCapRedisScript, keys, args...)
	}
	// Set res to 1 if frequency data is not configured
	res := make([]int64, len(targets))
	for i := range res {
		res[i] = 1
	}
	return res, nil
}

func (m *Manager) sendCampaignMessage(out messenger.Message, msg *CampaignMessage, logger push.Logger, numMsg int) error {
	startLog := time.Now()
	requestType := "campaign"

	// Send the message
	response, err := m.messengers[msg.Campaign.Messenger].Push(out)
	if err != nil {
		logger.Error().Msgf("Error sending message in campaign %s: subscriber %s: %v", msg.Campaign.Name, msg.Subscriber.UUID, err)
		m.updateCounterAndCheckCompletion("failure", msg, logger)
		// Log the failure
		failureLog := m.createLogEntry(
			requestType,
			msg.Campaign.Messenger,
			msg.Subscriber.Name,
			strings.ToLower(msg.Subscriber.Type),
			msg.Target,
			"failure",
			fmt.Sprintf("Failed to send message: %v", err),
			strconv.Itoa(msg.Campaign.ID),
		)
		m.SendMessengerLogs(failureLog)
		return err
	}

	logger.Info().Msgf("total time taken by campaign id: %v to get response= %v ms, terminalId = %v and current count = %v", msg.Campaign.ID, time.Since(startLog).Milliseconds(), msg.Subscriber.Name, numMsg)

	// Process and log the response
	successCount, err := m.processResponse(requestType, msg.Campaign.Messenger, msg.Subscriber.Name, strings.ToLower(msg.Subscriber.Type), msg.Target, strconv.Itoa(msg.Campaign.ID), response, logger)
	if successCount > 0 {
		m.updateCounterAndCheckCompletion("success", msg, logger)
	} else {
		m.updateCounterAndCheckCompletion("failure", msg, logger)
	}
	return err
}

func (m *Manager) sendTransactionMesage(out messenger.Message, msg *Message, logger push.Logger) error {
	requestType := "real_time_message"
	if msg.ProcessId != "" {
		requestType = "process"
	}

	// Send the message
	response, err := m.messengers[msg.Messenger].Push(out)
	if err != nil {
		logger.Error().Msgf("error sending message '%s': %v for subscriber: %s", msg.Subject, err, msg.Subscriber.Name)
		// Log the failure
		failureLog := m.createLogEntry(
			requestType,
			msg.Messenger,
			msg.Subscriber.Name,
			strings.ToLower(msg.Subscriber.Type),
			msg.Target,
			"failure",
			fmt.Sprintf("Failed to send message: %v", err),
			msg.ProcessId,
		)
		m.SendMessengerLogs(failureLog)
		return err
	}

	// Process and log the response
	_, err = m.processResponse(requestType, msg.Messenger, msg.Subscriber.Name, strings.ToLower(msg.Subscriber.Type), msg.Target, msg.ProcessId, response, logger)
	return err
}

func (m *Manager) processTransactionMessage(msg Message, logger push.Logger) error {
	var successTargets, rejectedTargets []string
	var err error

	if msg.ProcessId != "" && msg.DeDuplication {
		successTargets, rejectedTargets, err = processMessengerDedup(msg.Ctx, "process", msg.ProcessId, msg.Roles, msg.Target,
			msg.DuplicationLevel, msg.DirectFcm, msg.Subscriber.Name, msg.Subscriber.Type, msg.Messenger, msg.Cache, msg.ProcessDuration)
		if err != nil {
			logger.Error().Msgf("Error during deduplication: %v", err)
			return err
		}

		for _, rejectedTarget := range rejectedTargets {
			rejectedLog := m.createLogEntry(
				"process",
				msg.Messenger,
				msg.Subscriber.Name,
				strings.ToLower(msg.Subscriber.Type),
				rejectedTarget,
				"rejected",
				"rejected_due_to_duplication",
				msg.ProcessId,
			)
			m.SendMessengerLogs(rejectedLog)
		}

		if len(successTargets) == 0 {
			logger.Warn().Msgf("No valid targets for process ID %v and subscriber ID: %v", msg.ProcessId, msg.Subscriber.Name)
			return nil
		}
	}

	outMessage := m.createTransactionMessage(msg)
	err = m.sendTransactionMesage(outMessage, &msg, logger)
	return err
}

// Helper function to create a log entry
func (m *Manager) createLogEntry(requestType, messengerType, name, memberType, target, status, remarks, referenceID string) models.MessengerLog {
	return models.MessengerLog{
		RequestType:   requestType,
		MessengerType: messengerType,
		MemberName:    name,
		MemberType:    strings.ToLower(memberType),
		Target:        target,
		Status:        status,
		Remarks:       remarks,
		ReferenceID:   referenceID,
		CreatedBy:     "system",
	}
}

// Helper function to handle the response and log accordingly
func (m *Manager) processResponse(requestType, messengerType, name, memberType, target, referenceID, response string, logger push.Logger) (int, error) {
	successCount := 0
	if isSimpleStringResponse(response) {
		// Simple success response
		logEntry := m.createLogEntry(
			requestType,
			messengerType,
			name,
			memberType,
			target,
			"success",
			"Message sent successfully",
			referenceID,
		)
		m.SendMessengerLogs(logEntry)
		successCount++
		return successCount, nil
	}

	// Handle JSON response
	var results []external.NotificationResult
	if err := json.Unmarshal([]byte(response), &results); err != nil {
		logger.Error().Msgf("Error unmarshalling response for %s %s: %v", requestType, referenceID, err)
		return successCount, err
	}

	for _, result := range results {
		status := "success"
		remarks := "Message sent successfully"
		if !result.Success {
			status = "failure"
			remarks = fmt.Sprintf("Failed to send message: %s", result.Error)
		} else {
			successCount++
		}
		logEntry := m.createLogEntry(
			requestType,
			messengerType,
			name,
			memberType,
			result.Token,
			status,
			remarks,
			referenceID,
		)
		m.SendMessengerLogs(logEntry)
	}
	return successCount, nil
}

func (m *Manager) updateCounterAndCheckCompletion(counterName string, msg *CampaignMessage, logger push.Logger) {
	startLog := time.Now()
	var successDeliveries int64
	utils.UpdateCounter(strconv.Itoa(msg.Campaign.ID)+"_listmonk_counter_"+counterName, 1)
	if counterName == "success" {
		utils.UpdateCounter(strconv.Itoa(msg.Campaign.ID)+"_listmonk_success_deliveries", 1)
	}
	persec := utils.UpdateRateCounter(strconv.Itoa(msg.Campaign.ID) + "_listmonk_counter_perminute")
	if persec == 1 {
		utils.AddedExpiration(strconv.Itoa(msg.Campaign.ID)+"_listmonk_counter_perminute", 60)
	}
	value := utils.UpdateCounter(strconv.Itoa(msg.Campaign.ID)+"_listmonk_counter_total", 1)
	logger.Info().Msgf("Counters: total time taken to update all counters = %v microSeconds for campaignId = %v", time.Since(startLog).Microseconds(), msg.Campaign.ID)
	if value >= int64(msg.Campaign.ToSend) {
		m.store.HaltCampaign("completed", msg.Campaign.ID)
		m.RemoveCampaignMap(msg.Campaign.ID)
		m.BroadcastMessage(msg.Campaign.ID, "completed", msg.Ctx)
		successDeliveries = utils.UpdateCounter(strconv.Itoa(msg.Campaign.ID)+"_listmonk_success_deliveries", 0)
		if err := m.UpdateCampaignDelivered(logger, msg.Campaign.ID, successDeliveries); err != nil {
			logger.Error().Msgf("Error occured while updating delivered count for campaignID %v: %v", msg.Campaign.ID, err)
		} else {
			logger.Info().Msgf("Successfully updated delivered count for campaignID %v: %v", msg.Campaign.ID, err)
		}
		// Evict the corresponding set from Redis after the campaign is completed
		if err := utils.EvictCampaignSet(msg.Ctx, msg.Campaign.ID); err != nil {
			logger.Error().Msgf("Failed to evict set for campaignID %v: %v", msg.Campaign.ID, err)
		}
	}
}

func isSimpleStringResponse(response string) bool {
	return !strings.Contains(response, "\"Token\"")
}

func (m *Manager) appendHeaders(msg *CampaignMessage, out *messenger.Message) {
	h := textproto.MIMEHeader{}
	h.Set(models.EmailHeaderCampaignUUID, msg.Campaign.UUID)
	h.Set(models.EmailHeaderSubscriberUUID, msg.Subscriber.UUID)

	// Attach List-Unsubscribe headers?
	if m.cfg.UnsubHeader {
		h.Set("List-Unsubscribe-Post", "List-Unsubscribe=One-Click")
		h.Set("List-Unsubscribe", `<`+msg.unsubURL+`>`)
	}

	// Attach any custom headers.
	if len(msg.Campaign.Headers) > 0 {
		for _, set := range msg.Campaign.Headers {
			for hdr, val := range set {
				h.Add(hdr, val)
			}
		}
	}
	out.Headers = h
}

func (m *Manager) createCampaignMessage(msg *CampaignMessage, target string, uniqueTargets []string) messenger.Message {
	return messenger.Message{
		From:              msg.from,
		To:                []string{msg.to},
		Subject:           msg.subject,
		ContentType:       msg.Campaign.ContentType,
		Body:              msg.body,
		AltBody:           msg.altBody,
		Subscriber:        msg.Subscriber,
		Campaign:          msg.Campaign,
		RequestParams:     msg.RequestParams,
		Url:               msg.Url,
		RequestHeaders:    msg.RequestHeaders,
		RequestBody:       msg.RequestBody,
		Method:            msg.Method,
		Target:            target,
		CacheDetails:      msg.CacheDetails,
		Messenger:         msg.Campaign.Messenger,
		Ctx:               msg.Ctx,
		MsgId:             msg.MsgId,
		Data:              msg.Data,
		GatewayId:         msg.GatewayId,
		UniqueTargetsList: uniqueTargets,
	}
}

func (m *Manager) createTransactionMessage(msg Message) messenger.Message {
	return messenger.Message{
		From:             msg.From,
		To:               msg.To,
		Subject:          msg.Subject,
		ContentType:      msg.ContentType,
		Body:             msg.Body,
		AltBody:          msg.AltBody,
		Subscriber:       msg.Subscriber,
		Campaign:         msg.Campaign,
		AdditionalValues: msg.AdditionalValues,
		Label:            msg.Label,
		S3Files:          msg.S3Files,
		Encrypted:        msg.Encrypted,
		DirectFcm:        msg.DirectFcm,
		MessageType:      msg.MessageType,
		Url:              msg.Url,
		RequestParams:    msg.RequestParams,
		RequestHeaders:   msg.RequestHeaders,
		RequestBody:      msg.RequestBody,
		Method:           msg.Method,
		Cc:               msg.Cc,
		Bcc:              msg.Bcc,
		Roles:            msg.Roles,
		Ctx:              msg.Ctx,
		Data:             msg.Data,
		GatewayId:        msg.GatewayId,
		Messenger:        msg.Messenger,
	}
}

func removeDuplicatesFromFcm(fcmTokenList []string) []string {
	allKeys := make(map[string]bool)
	var list []string
	for _, item := range fcmTokenList {
		if _, value := allKeys[item]; !value {
			allKeys[item] = true
			list = append(list, item)
		}
	}
	return list
}

func processMessengerDedup(
	ctx context.Context,
	requestType string,
	referenceID string,
	roles string,
	target string,
	duplicationLevel string,
	directFCM bool,
	memberId string,
	memberType string,
	messenger string,
	cache models.Cache,
	processDuration int,
) ([]string, []string, error) {

	logger := ctx.Value(tracer.TRACE_LOGGER_KEY).(push.Logger)
	redisSetName := "listmonk_" + requestType + "_" + referenceID
	var targets []string

	// Fetch tokens or targets based on the messenger type
	switch messenger {
	case "fcm":
		if directFCM && memberId != "" {
			targets = []string{memberId}
		} else {
			targets = getFcmTokens(ctx, logger, memberId, roles)
		}
	case "mqtt":
		targets = GetIMEIData(ctx, logger, memberId, memberType)
	default:
		targets = []string{target} // For SMS, Email
	}

	if len(targets) == 0 {
		return nil, nil, fmt.Errorf("no valid targets found for messenger: %v and memberID: %v", messenger, memberId)
	}

	// Insert members into Redis and build search strings
	return buildAndInsertSearchStrings(
		ctx, referenceID, duplicationLevel, targets, messenger, redisSetName, cache, processDuration,
	)
}

func buildAndInsertSearchStrings(
	ctx context.Context,
	referenceID string,
	duplicationLevel string,
	targets []string,
	messenger string,
	redisSetName string,
	cache models.Cache,
	processDuration int,
) ([]string, []string, error) {
	var searchStrings []string
	for _, target := range targets {
		var searchString string
		searchString = buildSearchStringGeneric(referenceID, duplicationLevel, cache, target, messenger)
		searchStrings = append(searchStrings, searchString)
	}
	return utils.InsertMembersIntoSet(ctx, redisSetName, targets, searchStrings, processDuration)
}

// Helper function to get all unique IMEIs linked to a terminal
func GetIMEIData(ctx context.Context, logger push.Logger, memberId, memberType string) []string {
	if memberType == "" {
		memberType = "Terminal"
	}
	soundboxRequest := models.SoundboxRequest{
		MemberType: memberType,
		Members:    []string{memberId},
	}
	start := time.Now()
	soundboxResponse, err := external.GetIMEIForTerminal(soundboxRequest, ctx)
	logger.Info().Msgf("API: time taken to get IMEI for terminal %v from API = %v ms", memberId, time.Since(start).Milliseconds())

	if err != nil || soundboxResponse.Status != "Success" {
		if err != nil {
			logger.Error().Msgf("error occurred in mqttApi, details error %v", err.Error())
		} else {
			logger.Error().Msgf("error occurred in mqttApi, status: %v", soundboxResponse.Status)
		}
		return nil
	}

	var targets []string
	for _, j := range soundboxResponse.LinkedDevices {
		for _, k := range j.Devices {
			targets = append(targets, k.Imei)
		}
	}

	if len(targets) == 0 {
		logger.Info().Msgf("no imei found for the given request")
		return nil
	}
	return targets
}

// Helper function to get FCM tokens
func getFcmTokens(ctx context.Context, logger push.Logger, memberId string, roleId string) []string {
	start := time.Now()
	fcmTokens := cacheutils.GetCacheFcmData(memberId)
	logger.Info().Msgf("Redis: time taken to get terminal FCM tokens for %v = %v microseconds", memberId, time.Since(start).Microseconds())

	if len(fcmTokens) == 0 {
		start = time.Now()
		fcmTokens = fcm.GetFcmToken(memberId, ctx)
		logger.Info().Msgf("API: time taken to get terminal FCM tokens for %v from API = %v ms", memberId, time.Since(start).Milliseconds())
	}

	var tokens []string
	for _, fcmList := range fcmTokens {
		if fcmList.RoleId == roleId {
			tokens = append(tokens, fcmList.FcmTokens...)
		}
	}
	return tokens
}

// Helper function to get terminal data from cache or API
func getCacheTerminalData(ctx context.Context, logger push.Logger, memberId string) models.Cache {
	start := time.Now()
	cache := external.GetCacheTerminalData(memberId, ctx)
	logger.Info().Msgf("Redis: time taken to get terminal data for %v = %v microseconds", memberId, time.Since(start).Microseconds())
	return cache
}

func buildSearchStringGeneric(
	campaignId string,
	duplicationLevels string,
	cache models.Cache,
	target string,
	messenger string,
) string {

	searchString := campaignId
	levels := strings.Split(duplicationLevels, ",")

	// Handle default duplication levels based on the messenger type
	if len(levels) == 0 {
		switch messenger {
		case "sms":
			levels = append(levels, "mobileNumber")
		case "email":
			levels = append(levels, "emailId")
		case "mqtt":
			levels = append(levels, "imei")
		case "fcm":
			levels = append(levels, "device")
		}
	}

	for _, level := range levels {
		var cacheValue string

		trimmedLevel := strings.TrimSpace(level)
		if trimmedLevel == "" {
			continue
		}

		switch trimmedLevel {
		case "corporateId":
			cacheValue = cache.CorporateId
		case "terminalId":
			cacheValue = cache.TerminalId
		case "merchantId":
			cacheValue = cache.MerchantId
		case "dbaName":
			cacheValue = cache.DbaName
		}

		// Handle special case for FCM
		if messenger == "fcm" && trimmedLevel == "device" {
			fcmSplit := strings.Split(target, ":")
			if len(fcmSplit) > 0 {
				cacheValue = fcmSplit[0]
			}
		}

		// Default fallback to target if cacheValue is empty
		if cacheValue == "" {
			cacheValue = target
		}
		searchString += "_" + cacheValue
	}
	return searchString
}

func (m *Manager) RunV2() {
	// var publisher = utils.NatsPublisher{}
	// Spawn N message workers.
	m.broadcastSubject = m.publisher.GetSubject()
	var wg sync.WaitGroup
	for i := 0; i < m.cfg.Concurrency; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			m.workerV2()
		}()
	}

	go m.processLogs(m.db, m.logger)

	go func() {
		wg.Wait()
		close(m.logsChan)
	}()
}

func (m *Manager) processLogs(db *sqlx.DB, logger *log.Logger) {
	var logs []models.MessengerLog
	batchSize := 1000
	flushInterval := time.Second * 5 // Flush logs every 5 seconds if less than batch size

	// Start a timer to periodically flush logs
	timer := time.NewTicker(flushInterval)
	defer timer.Stop()

	logger.Println("Starting log processing...")

	for {
		select {
		// Case when a log is received from the channel
		case log, ok := <-m.logsChan:
			if !ok {
				// Channel is closed, insert any remaining logs and exit
				if len(logs) > 0 {
					logger.Printf("Final batch insertion of %d logs before exiting", len(logs))
					start := time.Now()
					_, err := messengerlog.InsertMessengerLogs(db, logger, logs)
					if err != nil {
						logger.Printf("Error occured while log insertion: ", err.Error())
					}
					elapsed := time.Since(start)
					logger.Printf("Final batch insertion took %v", elapsed)
				}
				logger.Println("Log processing stopped.")
				return
			}
			logs = append(logs, log)

			// Insert logs when the batch reaches the threshold
			if len(logs) >= batchSize {
				logger.Printf("Batch size reached: %d logs", len(logs))
				start := time.Now()
				_, err := messengerlog.InsertMessengerLogs(db, logger, logs)
				if err != nil {
					logger.Printf("Error occured while log insertion: ", err.Error())
				}
				elapsed := time.Since(start)
				logger.Printf("Batch insertion of %d logs took %v", len(logs), elapsed)
				logs = nil
			}

		// Case when the flush interval is reached
		case <-timer.C:
			// Insert logs if there are any remaining after the timeout
			if len(logs) > 0 {
				logger.Printf("Flush interval reached with %d logs in batch", len(logs))
				start := time.Now()
				_, err := messengerlog.InsertMessengerLogs(db, logger, logs)
				if err != nil {
					logger.Printf("Error occured while log insertion: ", err.Error())
				}
				elapsed := time.Since(start)
				logger.Printf("Timed batch insertion of %d logs took %v", len(logs), elapsed)
				logs = nil
			}
		}
	}
}

func (m *Manager) UpdateCampaignDelivered(logger push.Logger, campId int, successDeliveries int64) error {
	return m.store.UpdateCampaignDelivered(logger, campId, successDeliveries)
}

func (m *Manager) BroadcastMessage(campId int, status string, ctx context.Context) {
	var payload models.NatsBroadcastPayload

	payload.CampaignId = strconv.Itoa(campId)
	payload.CampaignStatus = status
	payload.EventId = uuid.NewString()
	payload.EventTime = time.Now().String()

	jsonData, _ := json.Marshal(payload)

	m.publisher.NewNatsMessage(jsonData, m.broadcastSubject, ctx)

}

func (m *Manager) HaltCampaign(status string, campId int) {
	m.store.HaltCampaign(status, campId)

}

func (m *Manager) RemoveCampaignMap(cId int) {

	m.campMapMut.Lock()
	defer m.campMapMut.Unlock()

	// camp, has := m.campaignMap[cId]

	delete(m.campaignMap, cId)

}

func (m *Manager) GetGatewayProperties() map[string]interface{} {
	return m.gatewayProps.Properties
}

func (m *Manager) GetGatewayName() map[string]interface{} {
	return m.gatewayProps.Gateway
}

func (m *Manager) GetBroadcastSubject() string {
	return m.broadcastSubject
}

func (m *Manager) AddCategoryMap(id int) error {

	m.catMapMut.Lock()

	defer m.catMapMut.Unlock()

	var categories []models.Category

	var err error

	if id > 0 {
		categories, err = m.store.GetCategoriesById(id)

	} else {
		categories, err = m.store.GetCategories()
	}

	if err != nil {
		return err
	}

	for _, val := range categories {
		var channel models.Channels
		var fcm models.ChannelOptions

		fcm.IsToggleable = val.IsToggleable.FCM
		fcm.IsVisible = val.IsVisibleToMerchant.FCM
		fcm.Value = val.NotificationDefaults.FCM

		channel.FCM = fcm

		var sms models.ChannelOptions

		sms.IsToggleable = val.IsToggleable.SMS
		sms.IsVisible = val.IsVisibleToMerchant.SMS
		sms.Value = val.NotificationDefaults.SMS

		channel.SMS = sms

		var email models.ChannelOptions

		email.IsToggleable = val.IsToggleable.Email
		email.IsVisible = val.IsVisibleToMerchant.Email
		email.Value = val.NotificationDefaults.Email

		channel.Email = email

		m.categoryMap[val.ID] = models.Preference{
			Name:     val.Name,
			ID:       val.ID,
			Channels: channel,
		}
	}
	return nil

}

func (m *Manager) GetCatFromMap(id int) models.Preference {
	m.catMapMut.RLock()
	defer m.catMapMut.RUnlock()
	cat := m.categoryMap[id]
	return cat
}

func (m *Manager) GetAllCatFromMap() map[int]models.PreferenceDTO {
	m.catMapMut.RLock()
	defer m.catMapMut.RUnlock()
	categories := make(map[int]models.PreferenceDTO)

	for _, pref := range m.categoryMap {

		categories[pref.ID] = models.PreferenceDTO{
			Name: pref.Name,
			ID:   pref.ID,
			Channels: models.RequestChannels{
				FCM: &models.ChannelOptions{
					IsToggleable: pref.Channels.FCM.IsToggleable,
					IsVisible:    pref.Channels.FCM.IsVisible,
					Value:        pref.Channels.FCM.Value,
				},
				SMS: &models.ChannelOptions{
					IsToggleable: pref.Channels.SMS.IsToggleable,
					IsVisible:    pref.Channels.SMS.IsVisible,
					Value:        pref.Channels.SMS.Value,
				},
				Email: &models.ChannelOptions{
					IsToggleable: pref.Channels.Email.IsToggleable,
					IsVisible:    pref.Channels.Email.IsVisible,
					Value:        pref.Channels.Email.Value,
				},
			},
		}
	}
	return categories
}

func (m *Manager) CheckPreference(name string, typeName string, catId int, messenger string) (string, error) {
	return m.store.CheckPreference(name, typeName, catId, messenger)
}

func (m *Manager) DeleteCategoryFromMap(id int) {

	m.catMapMut.Lock()
	defer m.catMapMut.Unlock()
	delete(m.categoryMap, id)
}
