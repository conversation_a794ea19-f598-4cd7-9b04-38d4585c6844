package manager

import (
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"mime"
	"net/http"
	"path/filepath"
	"strings"

	"github.com/knadh/listmonk/models"
)

func base64File(data []byte) string {

	enc := base64.StdEncoding.EncodeToString(data)
	return enc
}

func transformer(desired string, input map[string]interface{}) interface{} {

	mapper := make(map[string]string)
	json.Unmarshal([]byte(desired), &mapper)

	output := make(map[string]interface{})

	for key, value := range mapper {
		output[key] = input[value]
	}
	response, _ := json.Marshal(output)
	return response
}

func downloadFile(presignedURL string) ([]byte, error) {

	resp, err := http.Get(presignedURL)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.E<PERSON><PERSON>("request failed with status code: %d", resp.StatusCode)
	}

	fileContent, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %v", err)
	}

	return fileContent, nil
}

func objectListT(desired string, input []models.Files) interface{} {

	mapper := make(map[string]string)
	json.Unmarshal([]byte(desired), &mapper)
	result := make([]interface{}, 0)

	for _, item := range input {

		output := make(map[string]interface{})
		for key, value := range mapper {
			if strings.Contains(value, "Func") {
				newKey := strings.Replace(value, "Func", "", -1)
				newKey = strings.Replace(newKey, " ", "", -1)

				if newKey == "FileUrl" {
					file, err := downloadFile(item.FileUrl)
					if err == nil {
						output[key] = base64File(file)
					}
				} else if newKey == "MimeType" {
					output[key] = getMIME(item.FileName)
				}
			} else if value == "ContentType" {
				output[key] = item.ContentType
			} else if value == "FileUrl" {
				output[key] = item.FileUrl
			} else if value == "FileName" {
				output[key] = item.FileName
			} else if value == "Encoding" {
				output[key] = item.Encoding
			} else {
				output[key] = value
			}

		}
		result = append(result, output)
	}
	resultString, _ := json.Marshal(result)
	return string(resultString)
}

func getMIME(fileName string) string {
	ext := filepath.Ext(fileName)
	mimeType := mime.TypeByExtension(ext)
	return mimeType
}
