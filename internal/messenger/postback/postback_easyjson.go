// Code generated by easyj<PERSON> for marshaling/unmarshaling. DO NOT EDIT.

package postback

import (
	json "encoding/json"
	models "github.com/knadh/listmonk/models"
	easyjson "github.com/mailru/easyjson"
	jlexer "github.com/mailru/easyjson/jlexer"
	jwriter "github.com/mailru/easyjson/jwriter"
	textproto "net/textproto"
	time "time"
)

// suppress unused package warning
var (
	_ *json.RawMessage
	_ *jlexer.Lexer
	_ *jwriter.Writer
	_ easyjson.Marshaler
)

func easyjsonDf11841fDecodeGithubComKnadhListmonkInternalMessengerPostback(in *jlexer.Lexer, out *recipient) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "uuid":
			out.UUID = string(in.String())
		case "email":
			out.Email = string(in.String())
		case "name":
			out.Name = string(in.String())
		case "attribs":
			if in.IsNull() {
				in.Skip()
			} else {
				in.Delim('{')
				out.Attribs = make(models.JSON)
				for !in.IsDelim('}') {
					key := string(in.String())
					in.WantColon()
					var v1 interface{}
					if m, ok := v1.(easyjson.Unmarshaler); ok {
						m.UnmarshalEasyJSON(in)
					} else if m, ok := v1.(json.Unmarshaler); ok {
						_ = m.UnmarshalJSON(in.Raw())
					} else {
						v1 = in.Interface()
					}
					(out.Attribs)[key] = v1
					in.WantComma()
				}
				in.Delim('}')
			}
		case "status":
			out.Status = string(in.String())
		case "type":
			out.Type = string(in.String())
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjsonDf11841fEncodeGithubComKnadhListmonkInternalMessengerPostback(out *jwriter.Writer, in recipient) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"uuid\":"
		out.RawString(prefix[1:])
		out.String(string(in.UUID))
	}
	{
		const prefix string = ",\"email\":"
		out.RawString(prefix)
		out.String(string(in.Email))
	}
	{
		const prefix string = ",\"name\":"
		out.RawString(prefix)
		out.String(string(in.Name))
	}
	{
		const prefix string = ",\"attribs\":"
		out.RawString(prefix)
		if in.Attribs == nil && (out.Flags&jwriter.NilMapAsEmpty) == 0 {
			out.RawString(`null`)
		} else {
			out.RawByte('{')
			v2First := true
			for v2Name, v2Value := range in.Attribs {
				if v2First {
					v2First = false
				} else {
					out.RawByte(',')
				}
				out.String(string(v2Name))
				out.RawByte(':')
				if m, ok := v2Value.(easyjson.Marshaler); ok {
					m.MarshalEasyJSON(out)
				} else if m, ok := v2Value.(json.Marshaler); ok {
					out.Raw(m.MarshalJSON())
				} else {
					out.Raw(json.Marshal(v2Value))
				}
			}
			out.RawByte('}')
		}
	}
	{
		const prefix string = ",\"status\":"
		out.RawString(prefix)
		out.String(string(in.Status))
	}
	{
		const prefix string = ",\"type\":"
		out.RawString(prefix)
		out.String(string(in.Type))
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v recipient) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjsonDf11841fEncodeGithubComKnadhListmonkInternalMessengerPostback(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v recipient) MarshalEasyJSON(w *jwriter.Writer) {
	easyjsonDf11841fEncodeGithubComKnadhListmonkInternalMessengerPostback(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *recipient) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjsonDf11841fDecodeGithubComKnadhListmonkInternalMessengerPostback(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *recipient) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjsonDf11841fDecodeGithubComKnadhListmonkInternalMessengerPostback(l, v)
}
func easyjsonDf11841fDecodeGithubComKnadhListmonkInternalMessengerPostback1(in *jlexer.Lexer, out *postback) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "subject":
			out.Subject = string(in.String())
		case "content_type":
			out.ContentType = string(in.String())
		case "body":
			out.Body = string(in.String())
		case "additional_values":
			out.AdditionalValues = string(in.String())
		case "recipients":
			if in.IsNull() {
				in.Skip()
				out.Recipients = nil
			} else {
				in.Delim('[')
				if out.Recipients == nil {
					if !in.IsDelim(']') {
						out.Recipients = make([]recipient, 0, 0)
					} else {
						out.Recipients = []recipient{}
					}
				} else {
					out.Recipients = (out.Recipients)[:0]
				}
				for !in.IsDelim(']') {
					var v3 recipient
					(v3).UnmarshalEasyJSON(in)
					out.Recipients = append(out.Recipients, v3)
					in.WantComma()
				}
				in.Delim(']')
			}
		case "campaign":
			if in.IsNull() {
				in.Skip()
				out.Campaign = nil
			} else {
				if out.Campaign == nil {
					out.Campaign = new(campaign)
				}
				(*out.Campaign).UnmarshalEasyJSON(in)
			}
		case "attachments":
			if in.IsNull() {
				in.Skip()
				out.Attachments = nil
			} else {
				in.Delim('[')
				if out.Attachments == nil {
					if !in.IsDelim(']') {
						out.Attachments = make([]attachment, 0, 1)
					} else {
						out.Attachments = []attachment{}
					}
				} else {
					out.Attachments = (out.Attachments)[:0]
				}
				for !in.IsDelim(']') {
					var v4 attachment
					(v4).UnmarshalEasyJSON(in)
					out.Attachments = append(out.Attachments, v4)
					in.WantComma()
				}
				in.Delim(']')
			}
		case "label":
			out.Label = string(in.String())
		case "direct_fcm":
			out.DirectFcm = bool(in.Bool())
		case "messageType":
			out.MessageType = string(in.String())
		case "request_params":
			out.RequestParams = string(in.String())
		case "url":
			out.Url = string(in.String())
		case "request_headers":
			out.Headers = string(in.String())
		case "request_body":
			out.RequestBody = string(in.String())
		case "method":
			out.Method = string(in.String())
		case "roles":
			out.Roles = string(in.String())
		case "target":
			out.Target = string(in.String())
		case "cacheDetails":
			if in.IsNull() {
				in.Skip()
			} else {
				in.Delim('{')
				if !in.IsDelim('}') {
					out.CacheDetails = make(map[string]string)
				} else {
					out.CacheDetails = nil
				}
				for !in.IsDelim('}') {
					key := string(in.String())
					in.WantColon()
					var v5 string
					v5 = string(in.String())
					(out.CacheDetails)[key] = v5
					in.WantComma()
				}
				in.Delim('}')
			}
		case "messenger":
			out.Messenger = string(in.String())
		case "data":
			if in.IsNull() {
				in.Skip()
			} else {
				in.Delim('{')
				if !in.IsDelim('}') {
					out.Data = make(map[string]string)
				} else {
					out.Data = nil
				}
				for !in.IsDelim('}') {
					key := string(in.String())
					in.WantColon()
					var v6 string
					v6 = string(in.String())
					(out.Data)[key] = v6
					in.WantComma()
				}
				in.Delim('}')
			}
		case "tokens":
			if in.IsNull() {
				in.Skip()
				out.Tokens = nil
			} else {
				in.Delim('[')
				if out.Tokens == nil {
					if !in.IsDelim(']') {
						out.Tokens = make([]string, 0, 4)
					} else {
						out.Tokens = []string{}
					}
				} else {
					out.Tokens = (out.Tokens)[:0]
				}
				for !in.IsDelim(']') {
					var v7 string
					v7 = string(in.String())
					out.Tokens = append(out.Tokens, v7)
					in.WantComma()
				}
				in.Delim(']')
			}
		case "unique_targets_list":
			if in.IsNull() {
				in.Skip()
				out.UniqueTargetsList = nil
			} else {
				in.Delim('[')
				if out.UniqueTargetsList == nil {
					if !in.IsDelim(']') {
						out.UniqueTargetsList = make([]string, 0, 4)
					} else {
						out.UniqueTargetsList = []string{}
					}
				} else {
					out.UniqueTargetsList = (out.UniqueTargetsList)[:0]
				}
				for !in.IsDelim(']') {
					var v8 string
					v8 = string(in.String())
					out.UniqueTargetsList = append(out.UniqueTargetsList, v8)
					in.WantComma()
				}
				in.Delim(']')
			}
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjsonDf11841fEncodeGithubComKnadhListmonkInternalMessengerPostback1(out *jwriter.Writer, in postback) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"subject\":"
		out.RawString(prefix[1:])
		out.String(string(in.Subject))
	}
	{
		const prefix string = ",\"content_type\":"
		out.RawString(prefix)
		out.String(string(in.ContentType))
	}
	{
		const prefix string = ",\"body\":"
		out.RawString(prefix)
		out.String(string(in.Body))
	}
	{
		const prefix string = ",\"additional_values\":"
		out.RawString(prefix)
		out.String(string(in.AdditionalValues))
	}
	{
		const prefix string = ",\"recipients\":"
		out.RawString(prefix)
		if in.Recipients == nil && (out.Flags&jwriter.NilSliceAsEmpty) == 0 {
			out.RawString("null")
		} else {
			out.RawByte('[')
			for v9, v10 := range in.Recipients {
				if v9 > 0 {
					out.RawByte(',')
				}
				(v10).MarshalEasyJSON(out)
			}
			out.RawByte(']')
		}
	}
	{
		const prefix string = ",\"campaign\":"
		out.RawString(prefix)
		if in.Campaign == nil {
			out.RawString("null")
		} else {
			(*in.Campaign).MarshalEasyJSON(out)
		}
	}
	{
		const prefix string = ",\"attachments\":"
		out.RawString(prefix)
		if in.Attachments == nil && (out.Flags&jwriter.NilSliceAsEmpty) == 0 {
			out.RawString("null")
		} else {
			out.RawByte('[')
			for v11, v12 := range in.Attachments {
				if v11 > 0 {
					out.RawByte(',')
				}
				(v12).MarshalEasyJSON(out)
			}
			out.RawByte(']')
		}
	}
	{
		const prefix string = ",\"label\":"
		out.RawString(prefix)
		out.String(string(in.Label))
	}
	{
		const prefix string = ",\"direct_fcm\":"
		out.RawString(prefix)
		out.Bool(bool(in.DirectFcm))
	}
	{
		const prefix string = ",\"messageType\":"
		out.RawString(prefix)
		out.String(string(in.MessageType))
	}
	if in.RequestParams != "" {
		const prefix string = ",\"request_params\":"
		out.RawString(prefix)
		out.String(string(in.RequestParams))
	}
	if in.Url != "" {
		const prefix string = ",\"url\":"
		out.RawString(prefix)
		out.String(string(in.Url))
	}
	if in.Headers != "" {
		const prefix string = ",\"request_headers\":"
		out.RawString(prefix)
		out.String(string(in.Headers))
	}
	if in.RequestBody != "" {
		const prefix string = ",\"request_body\":"
		out.RawString(prefix)
		out.String(string(in.RequestBody))
	}
	if in.Method != "" {
		const prefix string = ",\"method\":"
		out.RawString(prefix)
		out.String(string(in.Method))
	}
	if in.Roles != "" {
		const prefix string = ",\"roles\":"
		out.RawString(prefix)
		out.String(string(in.Roles))
	}
	if in.Target != "" {
		const prefix string = ",\"target\":"
		out.RawString(prefix)
		out.String(string(in.Target))
	}
	if len(in.CacheDetails) != 0 {
		const prefix string = ",\"cacheDetails\":"
		out.RawString(prefix)
		{
			out.RawByte('{')
			v13First := true
			for v13Name, v13Value := range in.CacheDetails {
				if v13First {
					v13First = false
				} else {
					out.RawByte(',')
				}
				out.String(string(v13Name))
				out.RawByte(':')
				out.String(string(v13Value))
			}
			out.RawByte('}')
		}
	}
	{
		const prefix string = ",\"messenger\":"
		out.RawString(prefix)
		out.String(string(in.Messenger))
	}
	if len(in.Data) != 0 {
		const prefix string = ",\"data\":"
		out.RawString(prefix)
		{
			out.RawByte('{')
			v14First := true
			for v14Name, v14Value := range in.Data {
				if v14First {
					v14First = false
				} else {
					out.RawByte(',')
				}
				out.String(string(v14Name))
				out.RawByte(':')
				out.String(string(v14Value))
			}
			out.RawByte('}')
		}
	}
	if len(in.Tokens) != 0 {
		const prefix string = ",\"tokens\":"
		out.RawString(prefix)
		{
			out.RawByte('[')
			for v15, v16 := range in.Tokens {
				if v15 > 0 {
					out.RawByte(',')
				}
				out.String(string(v16))
			}
			out.RawByte(']')
		}
	}
	{
		const prefix string = ",\"unique_targets_list\":"
		out.RawString(prefix)
		if in.UniqueTargetsList == nil && (out.Flags&jwriter.NilSliceAsEmpty) == 0 {
			out.RawString("null")
		} else {
			out.RawByte('[')
			for v17, v18 := range in.UniqueTargetsList {
				if v17 > 0 {
					out.RawByte(',')
				}
				out.String(string(v18))
			}
			out.RawByte(']')
		}
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v postback) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjsonDf11841fEncodeGithubComKnadhListmonkInternalMessengerPostback1(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v postback) MarshalEasyJSON(w *jwriter.Writer) {
	easyjsonDf11841fEncodeGithubComKnadhListmonkInternalMessengerPostback1(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *postback) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjsonDf11841fDecodeGithubComKnadhListmonkInternalMessengerPostback1(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *postback) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjsonDf11841fDecodeGithubComKnadhListmonkInternalMessengerPostback1(l, v)
}
func easyjsonDf11841fDecodeGithubComKnadhListmonkInternalMessengerPostback2(in *jlexer.Lexer, out *campaign) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "id":
			out.Id = int(in.Int())
		case "from_email":
			out.FromEmail = string(in.String())
		case "uuid":
			out.UUID = string(in.String())
		case "name":
			out.Name = string(in.String())
		case "headers":
			if in.IsNull() {
				in.Skip()
				out.Headers = nil
			} else {
				in.Delim('[')
				if out.Headers == nil {
					if !in.IsDelim(']') {
						out.Headers = make(models.Headers, 0, 8)
					} else {
						out.Headers = models.Headers{}
					}
				} else {
					out.Headers = (out.Headers)[:0]
				}
				for !in.IsDelim(']') {
					var v19 map[string]string
					if in.IsNull() {
						in.Skip()
					} else {
						in.Delim('{')
						v19 = make(map[string]string)
						for !in.IsDelim('}') {
							key := string(in.String())
							in.WantColon()
							var v20 string
							v20 = string(in.String())
							(v19)[key] = v20
							in.WantComma()
						}
						in.Delim('}')
					}
					out.Headers = append(out.Headers, v19)
					in.WantComma()
				}
				in.Delim(']')
			}
		case "tags":
			if in.IsNull() {
				in.Skip()
				out.Tags = nil
			} else {
				in.Delim('[')
				if out.Tags == nil {
					if !in.IsDelim(']') {
						out.Tags = make([]string, 0, 4)
					} else {
						out.Tags = []string{}
					}
				} else {
					out.Tags = (out.Tags)[:0]
				}
				for !in.IsDelim(']') {
					var v21 string
					v21 = string(in.String())
					out.Tags = append(out.Tags, v21)
					in.WantComma()
				}
				in.Delim(']')
			}
		case "roles":
			if data := in.Raw(); in.Ok() {
				in.AddError((out.Roles).UnmarshalJSON(data))
			}
		case "fcmImage":
			if data := in.Raw(); in.Ok() {
				in.AddError((out.FcmImage).UnmarshalJSON(data))
			}
		case "cta":
			if data := in.Raw(); in.Ok() {
				in.AddError((out.Cta).UnmarshalJSON(data))
			}
		case "deDuplication":
			out.DeDuplication = bool(in.Bool())
		case "duplicationLevel":
			if data := in.Raw(); in.Ok() {
				in.AddError((out.DuplicationLevel).UnmarshalJSON(data))
			}
		case "data":
			if in.IsNull() {
				in.Skip()
			} else {
				in.Delim('{')
				out.Data = make(map[string]string)
				for !in.IsDelim('}') {
					key := string(in.String())
					in.WantColon()
					var v22 string
					v22 = string(in.String())
					(out.Data)[key] = v22
					in.WantComma()
				}
				in.Delim('}')
			}
		case "messageType":
			out.MessageType = string(in.String())
		case "label":
			out.Label = string(in.String())
		case "cacheDetails":
			if in.IsNull() {
				in.Skip()
			} else {
				in.Delim('{')
				if !in.IsDelim('}') {
					out.CacheDetails = make(map[string]string)
				} else {
					out.CacheDetails = nil
				}
				for !in.IsDelim('}') {
					key := string(in.String())
					in.WantColon()
					var v23 string
					v23 = string(in.String())
					(out.CacheDetails)[key] = v23
					in.WantComma()
				}
				in.Delim('}')
			}
		case "parentCampaignId":
			if data := in.Raw(); in.Ok() {
				in.AddError((out.ParentCampaignId).UnmarshalJSON(data))
			}
		case "parentCampaignName":
			if data := in.Raw(); in.Ok() {
				in.AddError((out.ParentCampaignName).UnmarshalJSON(data))
			}
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjsonDf11841fEncodeGithubComKnadhListmonkInternalMessengerPostback2(out *jwriter.Writer, in campaign) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"id\":"
		out.RawString(prefix[1:])
		out.Int(int(in.Id))
	}
	{
		const prefix string = ",\"from_email\":"
		out.RawString(prefix)
		out.String(string(in.FromEmail))
	}
	{
		const prefix string = ",\"uuid\":"
		out.RawString(prefix)
		out.String(string(in.UUID))
	}
	{
		const prefix string = ",\"name\":"
		out.RawString(prefix)
		out.String(string(in.Name))
	}
	{
		const prefix string = ",\"headers\":"
		out.RawString(prefix)
		if in.Headers == nil && (out.Flags&jwriter.NilSliceAsEmpty) == 0 {
			out.RawString("null")
		} else {
			out.RawByte('[')
			for v24, v25 := range in.Headers {
				if v24 > 0 {
					out.RawByte(',')
				}
				if v25 == nil && (out.Flags&jwriter.NilMapAsEmpty) == 0 {
					out.RawString(`null`)
				} else {
					out.RawByte('{')
					v26First := true
					for v26Name, v26Value := range v25 {
						if v26First {
							v26First = false
						} else {
							out.RawByte(',')
						}
						out.String(string(v26Name))
						out.RawByte(':')
						out.String(string(v26Value))
					}
					out.RawByte('}')
				}
			}
			out.RawByte(']')
		}
	}
	{
		const prefix string = ",\"tags\":"
		out.RawString(prefix)
		if in.Tags == nil && (out.Flags&jwriter.NilSliceAsEmpty) == 0 {
			out.RawString("null")
		} else {
			out.RawByte('[')
			for v27, v28 := range in.Tags {
				if v27 > 0 {
					out.RawByte(',')
				}
				out.String(string(v28))
			}
			out.RawByte(']')
		}
	}
	{
		const prefix string = ",\"roles\":"
		out.RawString(prefix)
		out.Raw((in.Roles).MarshalJSON())
	}
	{
		const prefix string = ",\"fcmImage\":"
		out.RawString(prefix)
		out.Raw((in.FcmImage).MarshalJSON())
	}
	{
		const prefix string = ",\"cta\":"
		out.RawString(prefix)
		out.Raw((in.Cta).MarshalJSON())
	}
	{
		const prefix string = ",\"deDuplication\":"
		out.RawString(prefix)
		out.Bool(bool(in.DeDuplication))
	}
	{
		const prefix string = ",\"duplicationLevel\":"
		out.RawString(prefix)
		out.Raw((in.DuplicationLevel).MarshalJSON())
	}
	{
		const prefix string = ",\"data\":"
		out.RawString(prefix)
		if in.Data == nil && (out.Flags&jwriter.NilMapAsEmpty) == 0 {
			out.RawString(`null`)
		} else {
			out.RawByte('{')
			v29First := true
			for v29Name, v29Value := range in.Data {
				if v29First {
					v29First = false
				} else {
					out.RawByte(',')
				}
				out.String(string(v29Name))
				out.RawByte(':')
				out.String(string(v29Value))
			}
			out.RawByte('}')
		}
	}
	{
		const prefix string = ",\"messageType\":"
		out.RawString(prefix)
		out.String(string(in.MessageType))
	}
	if in.Label != "" {
		const prefix string = ",\"label\":"
		out.RawString(prefix)
		out.String(string(in.Label))
	}
	if len(in.CacheDetails) != 0 {
		const prefix string = ",\"cacheDetails\":"
		out.RawString(prefix)
		{
			out.RawByte('{')
			v30First := true
			for v30Name, v30Value := range in.CacheDetails {
				if v30First {
					v30First = false
				} else {
					out.RawByte(',')
				}
				out.String(string(v30Name))
				out.RawByte(':')
				out.String(string(v30Value))
			}
			out.RawByte('}')
		}
	}
	{
		const prefix string = ",\"parentCampaignId\":"
		out.RawString(prefix)
		out.Raw((in.ParentCampaignId).MarshalJSON())
	}
	{
		const prefix string = ",\"parentCampaignName\":"
		out.RawString(prefix)
		out.Raw((in.ParentCampaignName).MarshalJSON())
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v campaign) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjsonDf11841fEncodeGithubComKnadhListmonkInternalMessengerPostback2(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v campaign) MarshalEasyJSON(w *jwriter.Writer) {
	easyjsonDf11841fEncodeGithubComKnadhListmonkInternalMessengerPostback2(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *campaign) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjsonDf11841fDecodeGithubComKnadhListmonkInternalMessengerPostback2(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *campaign) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjsonDf11841fDecodeGithubComKnadhListmonkInternalMessengerPostback2(l, v)
}
func easyjsonDf11841fDecodeGithubComKnadhListmonkInternalMessengerPostback3(in *jlexer.Lexer, out *attachment) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "name":
			out.Name = string(in.String())
		case "header":
			if in.IsNull() {
				in.Skip()
			} else {
				in.Delim('{')
				out.Header = make(textproto.MIMEHeader)
				for !in.IsDelim('}') {
					key := string(in.String())
					in.WantColon()
					var v31 []string
					if in.IsNull() {
						in.Skip()
						v31 = nil
					} else {
						in.Delim('[')
						if v31 == nil {
							if !in.IsDelim(']') {
								v31 = make([]string, 0, 4)
							} else {
								v31 = []string{}
							}
						} else {
							v31 = (v31)[:0]
						}
						for !in.IsDelim(']') {
							var v32 string
							v32 = string(in.String())
							v31 = append(v31, v32)
							in.WantComma()
						}
						in.Delim(']')
					}
					(out.Header)[key] = v31
					in.WantComma()
				}
				in.Delim('}')
			}
		case "content":
			if in.IsNull() {
				in.Skip()
				out.Content = nil
			} else {
				out.Content = in.Bytes()
			}
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjsonDf11841fEncodeGithubComKnadhListmonkInternalMessengerPostback3(out *jwriter.Writer, in attachment) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"name\":"
		out.RawString(prefix[1:])
		out.String(string(in.Name))
	}
	{
		const prefix string = ",\"header\":"
		out.RawString(prefix)
		if in.Header == nil && (out.Flags&jwriter.NilMapAsEmpty) == 0 {
			out.RawString(`null`)
		} else {
			out.RawByte('{')
			v34First := true
			for v34Name, v34Value := range in.Header {
				if v34First {
					v34First = false
				} else {
					out.RawByte(',')
				}
				out.String(string(v34Name))
				out.RawByte(':')
				if v34Value == nil && (out.Flags&jwriter.NilSliceAsEmpty) == 0 {
					out.RawString("null")
				} else {
					out.RawByte('[')
					for v35, v36 := range v34Value {
						if v35 > 0 {
							out.RawByte(',')
						}
						out.String(string(v36))
					}
					out.RawByte(']')
				}
			}
			out.RawByte('}')
		}
	}
	{
		const prefix string = ",\"content\":"
		out.RawString(prefix)
		out.Base64Bytes(in.Content)
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v attachment) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjsonDf11841fEncodeGithubComKnadhListmonkInternalMessengerPostback3(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v attachment) MarshalEasyJSON(w *jwriter.Writer) {
	easyjsonDf11841fEncodeGithubComKnadhListmonkInternalMessengerPostback3(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *attachment) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjsonDf11841fDecodeGithubComKnadhListmonkInternalMessengerPostback3(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *attachment) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjsonDf11841fDecodeGithubComKnadhListmonkInternalMessengerPostback3(l, v)
}
func easyjsonDf11841fDecodeGithubComKnadhListmonkInternalMessengerPostback4(in *jlexer.Lexer, out *Postback) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjsonDf11841fEncodeGithubComKnadhListmonkInternalMessengerPostback4(out *jwriter.Writer, in Postback) {
	out.RawByte('{')
	first := true
	_ = first
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v Postback) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjsonDf11841fEncodeGithubComKnadhListmonkInternalMessengerPostback4(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v Postback) MarshalEasyJSON(w *jwriter.Writer) {
	easyjsonDf11841fEncodeGithubComKnadhListmonkInternalMessengerPostback4(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *Postback) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjsonDf11841fDecodeGithubComKnadhListmonkInternalMessengerPostback4(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *Postback) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjsonDf11841fDecodeGithubComKnadhListmonkInternalMessengerPostback4(l, v)
}
func easyjsonDf11841fDecodeGithubComKnadhListmonkInternalMessengerPostback5(in *jlexer.Lexer, out *Options) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "name":
			out.Name = string(in.String())
		case "username":
			out.Username = string(in.String())
		case "password":
			out.Password = string(in.String())
		case "root_url":
			out.RootURL = string(in.String())
		case "max_conns":
			out.MaxConns = int(in.Int())
		case "retries":
			out.Retries = int(in.Int())
		case "timeout":
			out.Timeout = time.Duration(in.Int64())
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjsonDf11841fEncodeGithubComKnadhListmonkInternalMessengerPostback5(out *jwriter.Writer, in Options) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"name\":"
		out.RawString(prefix[1:])
		out.String(string(in.Name))
	}
	{
		const prefix string = ",\"username\":"
		out.RawString(prefix)
		out.String(string(in.Username))
	}
	{
		const prefix string = ",\"password\":"
		out.RawString(prefix)
		out.String(string(in.Password))
	}
	{
		const prefix string = ",\"root_url\":"
		out.RawString(prefix)
		out.String(string(in.RootURL))
	}
	{
		const prefix string = ",\"max_conns\":"
		out.RawString(prefix)
		out.Int(int(in.MaxConns))
	}
	{
		const prefix string = ",\"retries\":"
		out.RawString(prefix)
		out.Int(int(in.Retries))
	}
	{
		const prefix string = ",\"timeout\":"
		out.RawString(prefix)
		out.Int64(int64(in.Timeout))
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v Options) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjsonDf11841fEncodeGithubComKnadhListmonkInternalMessengerPostback5(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v Options) MarshalEasyJSON(w *jwriter.Writer) {
	easyjsonDf11841fEncodeGithubComKnadhListmonkInternalMessengerPostback5(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *Options) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjsonDf11841fDecodeGithubComKnadhListmonkInternalMessengerPostback5(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *Options) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjsonDf11841fDecodeGithubComKnadhListmonkInternalMessengerPostback5(l, v)
}
