package main

import (
	"bytes"
	"database/sql"
	"encoding/json"
	"fmt"
	"html/template"
	"image"
	"image/png"
	"io"
	"math"
	"net/http"
	"net/textproto"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/knadh/listmonk/external"
	"github.com/knadh/listmonk/internal/cacheutils"
	"github.com/knadh/listmonk/internal/fcm"
	"github.com/knadh/listmonk/internal/i18n"
	"github.com/knadh/listmonk/internal/messenger"
	"github.com/knadh/listmonk/models"
	"github.com/knadh/listmonk/tracer"
	"github.com/labstack/echo/v4"
	"github.com/lib/pq"
	push "github.com/phuslu/log"
	"gopkg.in/volatiletech/null.v6"
)

const (
	tplMessage = "message"
)

// tpl<PERSON>enderer wraps a template.tplRenderer for echo.
type tplRenderer struct {
	templates           *template.Template
	SiteName            string
	RootURL             string
	LogoURL             string
	FaviconURL          string
	EnablePublicSubPage bool
	EnablePublicArchive bool
}

// tplData is the data container that is injected
// into public templates for accessing data.
type tplData struct {
	SiteName            string
	RootURL             string
	LogoURL             string
	FaviconURL          string
	EnablePublicSubPage bool
	EnablePublicArchive bool
	Data                interface{}
	L                   *i18n.I18n
}

type publicTpl struct {
	Title       string
	Description string
}

type unsubTpl struct {
	publicTpl
	Subscriber       models.Subscriber
	Subscriptions    []models.Subscription
	SubUUID          string
	AllowBlocklist   bool
	AllowExport      bool
	AllowWipe        bool
	AllowPreferences bool
	ShowManage       bool
}

type optinTpl struct {
	publicTpl
	SubUUID   string
	ListUUIDs []string      `query:"l" form:"l"`
	Lists     []models.List `query:"-" form:"-"`
}

type msgTpl struct {
	publicTpl
	MessageTitle string
	Message      string
}

type subFormTpl struct {
	publicTpl
	Lists []models.List
}

var (
	pixelPNG = drawTransparentImage(3, 14)
	config   models.Config
)

// Render executes and renders a template for echo.
func (t *tplRenderer) Render(w io.Writer, name string, data interface{}, c echo.Context) error {
	return t.templates.ExecuteTemplate(w, name, tplData{
		SiteName:            t.SiteName,
		RootURL:             t.RootURL,
		LogoURL:             t.LogoURL,
		FaviconURL:          t.FaviconURL,
		EnablePublicSubPage: t.EnablePublicSubPage,
		EnablePublicArchive: t.EnablePublicArchive,
		Data:                data,
		L:                   c.Get("app").(*App).i18n,
	})
}

// handleGetPublicLists returns the list of public lists with minimal fields
// required to submit a subscription.
func handleGetPublicLists(c echo.Context) error {
	var (
		app    = c.Get("app").(*App)
		logger = c.Get("logger").(push.Logger)
	)

	// Get all public lists.
	lists, err := app.core.GetLists(models.ListTypePublic, logger)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, app.i18n.T("public.errorFetchingLists"))
	}

	type list struct {
		UUID string `json:"uuid"`
		Name string `json:"name"`
	}

	out := make([]list, 0, len(lists))
	for _, l := range lists {
		out = append(out, list{
			UUID: l.UUID,
			Name: l.Name,
		})
	}

	return c.JSON(http.StatusOK, out)
}

// handleViewCampaignMessage renders the HTML view of a campaign message.
// This is the view the {{ MessageURL }} template tag links to in e-mail campaigns.
func handleViewCampaignMessage(c echo.Context) error {
	var (
		app      = c.Get("app").(*App)
		campUUID = c.Param("campUUID")
		subUUID  = c.Param("subUUID")
		logger   = c.Get("logger").(push.Logger)
	)

	// Get the campaign.
	camp, err := app.core.GetCampaign(0, campUUID, logger)
	if err != nil {
		if er, ok := err.(*echo.HTTPError); ok {
			if er.Code == http.StatusBadRequest {
				return c.Render(http.StatusNotFound, tplMessage,
					makeMsgTpl(app.i18n.T("public.notFoundTitle"), "", app.i18n.T("public.campaignNotFound")))
			}
		}

		return c.Render(http.StatusInternalServerError, tplMessage,
			makeMsgTpl(app.i18n.T("public.errorTitle"), "", app.i18n.Ts("public.errorFetchingCampaign")))
	}

	// Get the subscriber.
	sub, err := app.core.GetSubscriber(0, subUUID, "", logger)
	if err != nil {
		if err == sql.ErrNoRows {
			return c.Render(http.StatusNotFound, tplMessage,
				makeMsgTpl(app.i18n.T("public.notFoundTitle"), "", app.i18n.T("public.errorFetchingEmail")))
		}

		return c.Render(http.StatusInternalServerError, tplMessage,
			makeMsgTpl(app.i18n.T("public.errorTitle"), "", app.i18n.Ts("public.errorFetchingCampaign")))
	}

	// Compile the template.
	if err := camp.CompileTemplate(app.manager.TemplateFuncs(&camp), false); err != nil {
		logger.Error().Msgf("error compiling template: %v", err)
		return c.Render(http.StatusInternalServerError, tplMessage,
			makeMsgTpl(app.i18n.T("public.errorTitle"), "", app.i18n.Ts("public.errorFetchingCampaign")))
	}

	// Render the message body.
	msg, err := app.manager.NewCampaignMessage(&camp, sub, tracer.WrapEchoContextLogger(c), "")
	if err != nil {
		logger.Error().Msgf("error rendering message: %v", err)
		return c.Render(http.StatusInternalServerError, tplMessage,
			makeMsgTpl(app.i18n.T("public.errorTitle"), "", app.i18n.Ts("public.errorFetchingCampaign")))
	}

	return c.HTML(http.StatusOK, string(msg.Body()))
}

// handleSubscriptionPage renders the subscription management page and
// handles unsubscriptions. This is the view that {{ UnsubscribeURL }} in
// campaigns link to.
func handleSubscriptionPage(c echo.Context) error {
	var (
		app           = c.Get("app").(*App)
		subUUID       = c.Param("subUUID")
		showManage, _ = strconv.ParseBool(c.FormValue("manage"))
		out           = unsubTpl{}
		logger        = c.Get("logger").(push.Logger)
	)
	out.SubUUID = subUUID
	out.Title = app.i18n.T("public.unsubscribeTitle")
	out.AllowBlocklist = app.constants.Privacy.AllowBlocklist
	out.AllowExport = app.constants.Privacy.AllowExport
	out.AllowWipe = app.constants.Privacy.AllowWipe
	out.AllowPreferences = app.constants.Privacy.AllowPreferences

	if app.constants.Privacy.AllowPreferences {
		out.ShowManage = showManage
	}

	// Get the subscriber's lists.
	subs, err := app.core.GetSubscriptions(0, subUUID, false, logger)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, app.i18n.T("public.errorFetchingLists"))
	}

	s, err := app.core.GetSubscriber(0, subUUID, "", logger)
	if err != nil {
		return c.Render(http.StatusInternalServerError, tplMessage,
			makeMsgTpl(app.i18n.T("public.errorTitle"), "", app.i18n.Ts("public.errorProcessingRequest")))
	}
	out.Subscriber = s

	if s.Status == models.SubscriberStatusBlockListed {
		return c.Render(http.StatusOK, tplMessage,
			makeMsgTpl(app.i18n.T("public.noSubTitle"), "", app.i18n.Ts("public.blocklisted")))
	}

	// Filter out unrelated private lists.
	if showManage {
		out.Subscriptions = make([]models.Subscription, 0, len(subs))
		for _, s := range subs {
			if s.Type == models.ListTypePrivate {
				if s.SubscriptionStatus.IsZero() {
					continue
				}

				s.Name = app.i18n.T("public.subPrivateList")
			}

			out.Subscriptions = append(out.Subscriptions, s)
		}
	}

	return c.Render(http.StatusOK, "subscription", out)
}

// handleSubscriptionPage renders the subscription management page and
// handles unsubscriptions. This is the view that {{ UnsubscribeURL }} in
// campaigns link to.
func handleSubscriptionPrefs(c echo.Context) error {
	var (
		app      = c.Get("app").(*App)
		campUUID = c.Param("campUUID")
		subUUID  = c.Param("subUUID")
		logger   = c.Get("logger").(push.Logger)

		req struct {
			Name      string   `form:"name" json:"name"`
			ListUUIDs []string `form:"l" json:"list_uuids"`
			Blocklist bool     `form:"blocklist" json:"blocklist"`
			Manage    bool     `form:"manage" json:"manage"`
		}
	)

	// Read the form.
	if err := c.Bind(&req); err != nil {
		return c.Render(http.StatusBadRequest, tplMessage,
			makeMsgTpl(app.i18n.T("public.errorTitle"), "", app.i18n.T("globals.messages.invalidData")))
	}

	// Simple unsubscribe.
	blocklist := app.constants.Privacy.AllowBlocklist && req.Blocklist
	if !req.Manage || blocklist {
		if err := app.core.UnsubscribeByCampaign(subUUID, campUUID, blocklist, logger); err != nil {
			return c.Render(http.StatusInternalServerError, tplMessage,
				makeMsgTpl(app.i18n.T("public.errorTitle"), "", app.i18n.T("public.errorProcessingRequest")))
		}

		return c.Render(http.StatusOK, tplMessage,
			makeMsgTpl(app.i18n.T("public.unsubbedTitle"), "", app.i18n.T("public.unsubbedInfo")))
	}

	// Is preference management enabled?
	if !app.constants.Privacy.AllowPreferences {
		return c.Render(http.StatusBadRequest, tplMessage,
			makeMsgTpl(app.i18n.T("public.errorTitle"), "", app.i18n.T("public.invalidFeature")))
	}

	// Manage preferences.
	req.Name = strings.TrimSpace(req.Name)
	if req.Name == "" || len(req.Name) > 256 {
		return c.Render(http.StatusBadRequest, tplMessage,
			makeMsgTpl(app.i18n.T("public.errorTitle"), "", app.i18n.T("subscribers.invalidName")))
	}

	// Get the subscriber from the DB.
	sub, err := app.core.GetSubscriber(0, subUUID, "", logger)
	if err != nil {
		return c.Render(http.StatusInternalServerError, tplMessage,
			makeMsgTpl(app.i18n.T("public.errorTitle"), "", app.i18n.Ts("globals.messages.pFound",
				"name", app.i18n.T("globals.terms.subscriber"))))
	}
	sub.Name = req.Name

	// Update name.
	if _, err := app.core.UpdateSubscriber(sub.ID, sub, logger); err != nil {
		return c.Render(http.StatusInternalServerError, tplMessage,
			makeMsgTpl(app.i18n.T("public.errorTitle"), "", app.i18n.T("public.errorProcessingRequest")))
	}

	// Get the subscriber's lists and whatever is not sent in the request (unchecked),
	// unsubscribe them.
	subs, err := app.core.GetSubscriptions(0, subUUID, false, logger)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, app.i18n.T("public.errorFetchingLists"))
	}
	reqUUIDs := make(map[string]struct{})
	for _, u := range req.ListUUIDs {
		reqUUIDs[u] = struct{}{}
	}

	unsubUUIDs := make([]string, 0, len(req.ListUUIDs))
	for _, s := range subs {
		if _, ok := reqUUIDs[s.UUID]; !ok {
			unsubUUIDs = append(unsubUUIDs, s.UUID)
		}
	}

	// Unsubscribe from lists.
	if err := app.core.UnsubscribeLists([]int64{sub.ID}, nil, unsubUUIDs, logger); err != nil {
		return c.Render(http.StatusInternalServerError, tplMessage,
			makeMsgTpl(app.i18n.T("public.errorTitle"), "", app.i18n.T("public.errorProcessingRequest")))

	}

	return c.Render(http.StatusOK, tplMessage,
		makeMsgTpl(app.i18n.T("globals.messages.done"), "", app.i18n.T("public.prefsSaved")))
}

// handleOptinPage renders the double opt-in confirmation page that subscribers
// see when they click on the "Confirm subscription" button in double-optin
// notifications.
func handleOptinPage(c echo.Context) error {
	var (
		app        = c.Get("app").(*App)
		subUUID    = c.Param("subUUID")
		confirm, _ = strconv.ParseBool(c.FormValue("confirm"))
		out        = optinTpl{}
		logger     = c.Get("logger").(push.Logger)
	)
	out.SubUUID = subUUID
	out.Title = app.i18n.T("public.confirmOptinSubTitle")
	out.SubUUID = subUUID

	// Get and validate fields.
	if err := c.Bind(&out); err != nil {
		return err
	}

	// Validate list UUIDs if there are incoming UUIDs in the request.
	if len(out.ListUUIDs) > 0 {
		for _, l := range out.ListUUIDs {
			if !reUUID.MatchString(l) {
				return c.Render(http.StatusBadRequest, tplMessage,
					makeMsgTpl(app.i18n.T("public.errorTitle"), "", app.i18n.T("globals.messages.invalidUUID")))
			}
		}
	}

	// Get the list of subscription lists where the subscriber hasn't confirmed.
	lists, err := app.core.GetSubscriberLists(0, subUUID, nil, out.ListUUIDs, models.SubscriptionStatusUnconfirmed, "", logger)
	if err != nil {
		return c.Render(http.StatusInternalServerError, tplMessage,
			makeMsgTpl(app.i18n.T("public.errorTitle"), "", app.i18n.Ts("public.errorFetchingLists")))
	}

	// There are no lists to confirm.
	if len(lists) == 0 {
		return c.Render(http.StatusOK, tplMessage,
			makeMsgTpl(app.i18n.T("public.noSubTitle"), "", app.i18n.Ts("public.noSubInfo")))
	}
	out.Lists = lists

	// Confirm.
	if confirm {
		if err := app.core.ConfirmOptionSubscription(subUUID, out.ListUUIDs, logger); err != nil {
			logger.Error().Msgf("error unsubscribing: %v", err)
			return c.Render(http.StatusInternalServerError, tplMessage,
				makeMsgTpl(app.i18n.T("public.errorTitle"), "", app.i18n.Ts("public.errorProcessingRequest")))
		}

		return c.Render(http.StatusOK, tplMessage,
			makeMsgTpl(app.i18n.T("public.subConfirmedTitle"), "", app.i18n.Ts("public.subConfirmed")))
	}

	return c.Render(http.StatusOK, "optin", out)
}

// handleSubscriptionFormPage handles subscription requests coming from public
// HTML subscription forms.
func handleSubscriptionFormPage(c echo.Context) error {
	var (
		app    = c.Get("app").(*App)
		logger = c.Get("logger").(push.Logger)
	)

	if !app.constants.EnablePublicSubPage {
		return c.Render(http.StatusNotFound, tplMessage,
			makeMsgTpl(app.i18n.T("public.errorTitle"), "", app.i18n.Ts("public.invalidFeature")))
	}

	// Get all public lists.
	lists, err := app.core.GetLists(models.ListTypePublic, logger)
	if err != nil {
		return c.Render(http.StatusInternalServerError, tplMessage,
			makeMsgTpl(app.i18n.T("public.errorTitle"), "", app.i18n.Ts("public.errorFetchingLists")))
	}

	if len(lists) == 0 {
		return c.Render(http.StatusInternalServerError, tplMessage,
			makeMsgTpl(app.i18n.T("public.errorTitle"), "", app.i18n.Ts("public.noListsAvailable")))
	}

	out := subFormTpl{}
	out.Title = app.i18n.T("public.sub")
	out.Lists = lists

	return c.Render(http.StatusOK, "subscription-form", out)
}

// handleSubscriptionForm handles subscription requests coming from public
// HTML subscription forms.
func handleSubscriptionForm(c echo.Context) error {
	var (
		app = c.Get("app").(*App)
	)

	// If there's a nonce value, a bot could've filled the form.
	if c.FormValue("nonce") != "" {
		return echo.NewHTTPError(http.StatusBadGateway, app.i18n.T("public.invalidFeature"))
	}

	hasOptin, err := processSubForm(c)
	if err != nil {
		e, ok := err.(*echo.HTTPError)
		if !ok {
			return e
		}

		return c.Render(e.Code, tplMessage,
			makeMsgTpl(app.i18n.T("public.errorTitle"), "", fmt.Sprintf("%s", e.Message)))
	}

	msg := "public.subConfirmed"
	if hasOptin {
		msg = "public.subOptinPending"
	}

	return c.Render(http.StatusOK, tplMessage, makeMsgTpl(app.i18n.T("public.subTitle"), "", app.i18n.Ts(msg)))
}

// handleSubscriptionForm handles subscription requests coming from public
// API calls.
func handlePublicSubscription(c echo.Context) error {
	hasOptin, err := processSubForm(c)
	if err != nil {
		return err
	}

	return c.JSON(http.StatusOK, okResp{struct {
		HasOptin bool `json:"has_optin"`
	}{hasOptin}})
}

// handleLinkRedirect redirects a link UUID to its original underlying link
// after recording the link click for a particular subscriber in the particular
// campaign. These links are generated by {{ TrackLink }} tags in campaigns.
func handleLinkRedirect(c echo.Context) error {
	var (
		app      = c.Get("app").(*App)
		linkUUID = c.Param("linkUUID")
		campUUID = c.Param("campUUID")
		subUUID  = c.Param("subUUID")
		logger   = c.Get("logger").(push.Logger)
	)

	// If individual tracking is disabled, do not record the subscriber ID.
	if !app.constants.Privacy.IndividualTracking {
		subUUID = ""
	}

	url, err := app.core.RegisterCampaignLinkClick(linkUUID, campUUID, subUUID, logger)
	if err != nil {
		e := err.(*echo.HTTPError)
		return c.Render(e.Code, tplMessage, makeMsgTpl(app.i18n.T("public.errorTitle"), "", e.Error()))
	}

	return c.Redirect(http.StatusTemporaryRedirect, url)
}

// handleRegisterCampaignView registers a campaign view which comes in
// the form of an pixel image request. Regardless of errors, this handler
// should always render the pixel image bytes. The pixel URL is is generated by
// the {{ TrackView }} template tag in campaigns.
func handleRegisterCampaignView(c echo.Context) error {
	var (
		app      = c.Get("app").(*App)
		campUUID = c.Param("campUUID")
		subUUID  = c.Param("subUUID")
		logger   = c.Get("logger").(push.Logger)
	)

	// If individual tracking is disabled, do not record the subscriber ID.
	if !app.constants.Privacy.IndividualTracking {
		subUUID = ""
	}

	// Exclude dummy hits from template previews.
	if campUUID != dummyUUID && subUUID != dummyUUID {
		if err := app.core.RegisterCampaignView(campUUID, subUUID, logger); err != nil {
			logger.Error().Msgf("error registering campaign view: %s", err)
		}
	}

	c.Response().Header().Set("Cache-Control", "no-cache")
	return c.Blob(http.StatusOK, "image/png", pixelPNG)
}

// handleSelfExportSubscriberData pulls the subscriber's profile, list subscriptions,
// campaign views and clicks and produces a JSON report that is then e-mailed
// to the subscriber. This is a privacy feature and the data that's exported
// is dependent on the configuration.
func handleSelfExportSubscriberData(c echo.Context) error {
	var (
		app     = c.Get("app").(*App)
		subUUID = c.Param("subUUID")
		logger  = c.Get("logger").(push.Logger)
	)
	// Is export allowed?
	if !app.constants.Privacy.AllowExport {
		return c.Render(http.StatusBadRequest, tplMessage,
			makeMsgTpl(app.i18n.T("public.errorTitle"), "", app.i18n.Ts("public.invalidFeature")))
	}

	// Get the subscriber's data. A single query that gets the profile,
	// list subscriptions, campaign views, and link clicks. Names of
	// private lists are replaced with "Private list".
	data, b, err := exportSubscriberData(0, subUUID, app.constants.Privacy.Exportable, app, logger)
	if err != nil {
		logger.Error().Msgf("error exporting subscriber data: %s", err)
		return c.Render(http.StatusInternalServerError, tplMessage,
			makeMsgTpl(app.i18n.T("public.errorTitle"), "", app.i18n.Ts("public.errorProcessingRequest")))
	}

	// Prepare the attachment e-mail.
	var msg bytes.Buffer
	if err := app.notifTpls.tpls.ExecuteTemplate(&msg, notifSubscriberData, data); err != nil {
		logger.Error().Msgf("error compiling notification template '%s': %v", notifSubscriberData, err)
		return c.Render(http.StatusInternalServerError, tplMessage,
			makeMsgTpl(app.i18n.T("public.errorTitle"), "", app.i18n.Ts("public.errorProcessingRequest")))
	}

	// Send the data as a JSON attachment to the subscriber.
	const fname = "data.json"
	if _, err := app.messengers[emailMsgr].Push(messenger.Message{
		ContentType: app.notifTpls.contentType,
		From:        app.constants.FromEmail,
		To:          []string{data.Email},
		Subject:     "Your data",
		Body:        msg.Bytes(),
		Attachments: []messenger.Attachment{
			{
				Name:    fname,
				Content: b,
				Header:  messenger.MakeAttachmentHeader(fname, "base64"),
			},
		},
	}); err != nil {
		logger.Error().Msgf("error e-mailing subscriber profile: %s", err)
		return c.Render(http.StatusInternalServerError, tplMessage,
			makeMsgTpl(app.i18n.T("public.errorTitle"), "", app.i18n.Ts("public.errorProcessingRequest")))
	}

	return c.Render(http.StatusOK, tplMessage,
		makeMsgTpl(app.i18n.T("public.dataSentTitle"), "", app.i18n.T("public.dataSent")))
}

// handleWipeSubscriberData allows a subscriber to delete their data. The
// profile and subscriptions are deleted, while the campaign_views and link
// clicks remain as orphan data unconnected to any subscriber.
func handleWipeSubscriberData(c echo.Context) error {
	var (
		app     = c.Get("app").(*App)
		subUUID = c.Param("subUUID")
		logger  = c.Get("logger").(push.Logger)
	)

	// Is wiping allowed?
	if !app.constants.Privacy.AllowWipe {
		return c.Render(http.StatusBadRequest, tplMessage,
			makeMsgTpl(app.i18n.T("public.errorTitle"), "", app.i18n.Ts("public.invalidFeature")))
	}

	if err := app.core.DeleteSubscribers(nil, []string{subUUID}, logger); err != nil {
		logger.Error().Msgf("error wiping subscriber data: %s", err)
		return c.Render(http.StatusInternalServerError, tplMessage,
			makeMsgTpl(app.i18n.T("public.errorTitle"), "", app.i18n.Ts("public.errorProcessingRequest")))
	}

	return c.Render(http.StatusOK, tplMessage,
		makeMsgTpl(app.i18n.T("public.dataRemovedTitle"), "", app.i18n.T("public.dataRemoved")))
}

// drawTransparentImage draws a transparent PNG of given dimensions
// and returns the PNG bytes.
func drawTransparentImage(h, w int) []byte {
	var (
		img = image.NewRGBA(image.Rect(0, 0, w, h))
		out = &bytes.Buffer{}
	)
	_ = png.Encode(out, img)
	return out.Bytes()
}

// processSubForm processes an incoming form/public API subscription request.
// The bool indicates whether there was subscription to an optin list so that
// an appropriate message can be shown.
func processSubForm(c echo.Context) (bool, error) {
	var (
		app = c.Get("app").(*App)
		req struct {
			Name          string   `form:"name" json:"name"`
			Email         string   `form:"email" json:"email"`
			FormListUUIDs []string `form:"l" json:"list_uuids"`
		}
		logger = c.Get("logger").(push.Logger)
	)

	// Get and validate fields.
	if err := c.Bind(&req); err != nil {
		return false, err
	}

	if len(req.FormListUUIDs) == 0 {
		return false, echo.NewHTTPError(http.StatusBadRequest, app.i18n.T("public.noListsSelected"))
	}

	// If there's no name, use the name bit from the e-mail.
	req.Name = strings.TrimSpace(req.Name)
	if req.Name == "" {
		req.Name = strings.Split(req.Email, "@")[0]
	}

	// Validate fields.
	//Commented by Deepali
	// if len(req.Email) > 1000 {
	// 	return false, echo.NewHTTPError(http.StatusBadRequest, app.i18n.T("subscribers.invalidEmail"))
	// }

	em, err := app.importer.SanitizeEmail(req.Email)
	if err != nil {
		return false, echo.NewHTTPError(http.StatusBadRequest, err.Error())
	}
	req.Email = em

	req.Name = strings.TrimSpace(req.Name)
	if len(req.Name) == 0 || len(req.Name) > stdInputMaxLen {
		return false, echo.NewHTTPError(http.StatusBadRequest, app.i18n.T("subscribers.invalidName"))
	}

	listUUIDs := pq.StringArray(req.FormListUUIDs)

	// Insert the subscriber into the DB.
	_, hasOptin, err := app.core.InsertSubscriber(models.Subscriber{
		Name:   req.Name,
		Email:  req.Email,
		Status: models.SubscriberStatusEnabled,
	}, nil, listUUIDs, false, tracer.WrapEchoContextLogger(c))
	if err != nil {
		// Subscriber already exists. Update subscriptions.
		if e, ok := err.(*echo.HTTPError); ok && e.Code == http.StatusConflict {
			sub, err := app.core.GetSubscriber(0, "", req.Email, logger)
			if err != nil {
				return false, err
			}

			if _, err := app.core.UpdateSubscriberWithLists(sub.ID, sub, nil, listUUIDs, false, false, logger); err != nil {
				return false, err
			}

			return false, nil
		}

		return false, echo.NewHTTPError(http.StatusInternalServerError, fmt.Sprintf("%s", err.(*echo.HTTPError).Message))
	}

	return hasOptin, nil
}

type campaign struct {
	FromEmail          string            `json:"from_email"`
	UUID               string            `json:"uuid"`
	Name               string            `json:"name"`
	Headers            models.Headers    `json:"headers"`
	Tags               []string          `json:"tags"`
	Roles              string            `json:"roles"`
	FcmImage           string            `json:"fcmImage"`
	Cta                string            `json:"cta"`
	DeDuplication      bool              `json:"deDuplication"`
	DuplicationLevel   null.String       `json:"duplicationLevel"`
	Id                 int               `json:"id"`
	Data               map[string]string `json:"data"`
	MessageType        string            `json:"messageType"`
	Label              string            `json:"label"`
	ParentCampaignId   null.String       `json:"parentCampaignId"`
	ParentCampaignName null.String       `json:"parentCampaignName"`
}

type recipient struct {
	UUID    string      `json:"uuid"`
	Email   string      `json:"email"`
	Name    string      `json:"name"`
	Attribs models.JSON `json:"attribs"`
	Status  string      `json:"status"`
	Type    string      `json:"type"`
}

type attachment struct {
	Name    string               `json:"name"`
	Header  textproto.MIMEHeader `json:"header"`
	Content []byte               `json:"content"`
}

func handleGenericMessenger(c echo.Context) error {
	var (
		logger = c.Get("logger").(push.Logger)
		req    struct {
			Subject        string            `json:"subject"`
			ContentType    string            `json:"content_type"`
			Body           string            `json:"body"`
			Recipients     []recipient       `json:"recipients"`
			Campaign       *campaign         `json:"campaign"`
			Attachments    []attachment      `json:"attachments"`
			RequestParams  string            `json:"request_params,omitempty"`
			Url            string            `json:"url,omitempty"`
			RequestHeaders string            `json:"request_headers,omitempty"`
			RequestBody    string            `json:"request_body,omitempty"`
			Method         string            `json:"method,omitempty"`
			Target         string            `json:"target,omitempty"`
			Messenger      string            `json:"messenger"`
			CacheDetails   map[string]string `json:"cacheDetails,omitempty"`
		}
	)
	var results string
	var err error
	// Get and validate fields.
	if err := c.Bind(&req); err != nil {
		logger.Error().Msgf("error reading request body: %v", err)
		return err
	}

	for _, item := range req.Recipients {
		logger.Info().Msgf("Sending %v to: %v", req.Messenger, item.Name)
		results, err = SendMessageV2(req.RequestParams, req.Url, req.RequestHeaders, req.RequestBody, req.Method, req.Messenger, tracer.WrapEchoContextLogger(c))
		if err != nil {
			logger.Error().Msgf("%v Messenger: Got error while sending message to %v, err= %v", req.Messenger, item.Name, err)
			return c.JSON(http.StatusInternalServerError, map[string]string{
				"error": "Failed to send message",
			})
		}

	}
	return c.JSON(http.StatusOK, results)

}

func handleMockRequest(c echo.Context) error {
	var logger = c.Get("logger").(push.Logger)
	body := c.Request().Body
	buffer := new(strings.Builder)
	_, err := io.Copy(buffer, body)
	if err != nil {
		return err
	}

	// Print the request body
	logger.Info().Msgf("Request Body: %s", buffer.String())

	// Optionally print other request details
	logger.Info().Msgf("Method: %s", c.Request().Method)
	logger.Info().Msgf("URL: %s", c.Request().URL.String())
	logger.Info().Msgf("Headers: %v", c.Request().Header)

	// Send a response
	return c.JSON(http.StatusOK, map[string]string{
		"message": "Request details printed",
	})
}

// func handleSmsMessengerDedup(target string, duplicationLevel null.String, campId int, cacheDetails map[string]string, logger push.Logger) (bool, error) {

// 	duplicationLevelList := strings.Split(duplicationLevel.String, ",")

// 	if len(duplicationLevelList) == 0 {
// 		duplicationLevelList = append(duplicationLevelList, "mobileNumber")
// 	}

// 	searchString := strconv.Itoa(campId)

// 	for _, level := range duplicationLevelList {

// 		trimedLevel := strings.TrimSpace(level)

// 		if trimedLevel == "" {
// 			continue
// 		}
// 		if level == "corporateId" {
// 			searchString += "_" + cacheDetails["corporateId"]
// 		} else if level == "terminalId" {
// 			searchString += "_" + cacheDetails["terminalId"]
// 		} else if level == "merchantId" {
// 			searchString += "_" + cacheDetails["merchantId"]
// 		} else if level == "dbaName" {
// 			searchString += "_" + cacheDetails["dbaName"]
// 		} else {
// 			searchString += "_" + target
// 		}

// 	}

// 	stmt, err := db.Prepare("INSERT INTO campaign_targets(campaign_id, target, terminal_id) values($1, $2, $3) on conflict (target) do nothing")
// 	if err != nil {

// 		logger.Error().Msgf("Error occured while getting insert statement, detailed error = %v", err.Error())
// 		return false, err
// 	}

// 	defer stmt.Close()

// 	res, err := stmt.Exec(campId, searchString, cacheDetails["terminalId"])

// 	if err != nil {
// 		logger.Error().Msgf("Error occured while inserting statement, detailed error = %v", err.Error())
// 		return false, err
// 	}
// 	numRowsAffected, err := res.RowsAffected()

// 	if err != nil {
// 		logger.Error().Msgf("Error occured while inserting statement, detailed error = %v", err.Error())
// 		return false, err
// 	}

// 	if numRowsAffected > 0 {
// 		return true, nil
// 	}
// 	return false, nil
// }

func convertAdditionalKeyValsInFormat(jsonStr string, logger push.Logger) map[string]string {

	var jsonArray []map[string]string
	jsonObject := make(map[string]string)
	err := json.Unmarshal([]byte(jsonStr), &jsonArray)
	if err != nil {
		logger.Error().Msgf("Error while parsing additional_values :%v", err)
		return jsonObject
	}
	// Build the JSON object
	for _, elem := range jsonArray {
		jsonObject[elem["key"]] = elem["value"]
	}
	return jsonObject

}

type FCMRequest struct {
	Subject           string            `json:"subject"`
	ContentType       string            `json:"content_type"`
	Body              string            `json:"body"`
	Recipients        []recipient       `json:"recipients"`
	Campaign          *campaign         `json:"campaign"`
	Attachments       []attachment      `json:"attachments"`
	Label             string            `json:"label"`
	AdditionalValues  string            `json:"additional_values"`
	DirectFcm         bool              `json:"direct_fcm"`
	MessageType       string            `json:"messageType"`
	Roles             string            `json:"roles"`
	Data              map[string]string `json:"data"`
	UniqueTargetsList []string          `json:"unique_targets_list"`
}

func handleFcmMessenger(c echo.Context) error {
	var (
		logger = c.Get("logger").(push.Logger)
		req    FCMRequest
	)
	var results []external.NotificationResult
	var err error

	// Bind and validate request body
	if err := c.Bind(&req); err != nil {
		logger.Error().Msgf("error reading request body: %v", err)
		return c.JSON(http.StatusBadRequest, map[string]string{
			"error": "Invalid request body",
		})
	}

	label, processedReq, fcmData := processFcmData(req, c)

	// If there are tokens, send notifications in batches
	if len(processedReq.UniqueTargetsList) > 0 {
		tokens := processedReq.UniqueTargetsList
		if results, err = sendFcmNotifications(processedReq, fcmData, tokens, label, logger); err != nil {
			logger.Error().Msgf("Failed to send FCM notifications: %v", err)
			return c.JSON(http.StatusInternalServerError, map[string]string{
				"error": "Failed to send FCM notifications",
			})
		}
	}
	return c.JSON(http.StatusOK, results)
}

func processFcmData(req FCMRequest, ctx echo.Context) (string, FCMRequest, map[string]string) {
	var label string
	fcmData := make(map[string]string)
	roles := "all"
	logger := ctx.Get("logger").(push.Logger)

	//extract the data field for firebase message
	if len(req.AdditionalValues) != 0 {
		fcmData = convertAdditionalKeyValsInFormat(req.AdditionalValues, logger)
	}

	if req.Data == nil {
		req.Data = make(map[string]string)
	}

	// Process campaign data
	if req.Campaign != nil {
		roles = req.Campaign.Roles
		label = req.Campaign.Label
		req.Data["campaignName"] = req.Campaign.Name
		req.Data["ts"] = fmt.Sprintf("%d", time.Now().Local().UnixMilli())
		req.Data["campUid"] = req.Campaign.UUID
		req.Data["campaignId"] = strconv.Itoa(req.Campaign.Id)

		if req.Campaign.ParentCampaignId.Valid {
			req.Data["parentCampaignId"] = req.Campaign.ParentCampaignId.String
		} else {
			req.Data["parentCampaignId"] = ""
		}
		if req.Campaign.ParentCampaignName.Valid {
			req.Data["parentCampaignName"] = req.Campaign.ParentCampaignName.String
		} else {
			req.Data["parentCampaignName"] = ""
		}

		if len(req.Recipients) > 0 {
			req.Campaign.Data["terminalId"] = req.Recipients[0].Name
		}

	} else {
		fcmData["ts"] = fmt.Sprintf("%d", time.Now().Local().UnixMilli())
		label, _ = sanitizeString(req.Label)

		if len(req.Roles) != 0 {
			roles = req.Roles
		}
		var tokens []string

		if req.DirectFcm {
			for _, item := range req.Recipients {
				tokens = append(tokens, item.Name)
			}

		} else {
			for _, item := range req.Recipients {
				fcmTokens := cacheutils.GetCacheFcmData(item.Name)
				if len(fcmTokens) == 0 {
					fcmTokens = fcm.GetFcmToken(item.Name, tracer.WrapEchoContextLogger(ctx))
				}

				for _, fcmList := range fcmTokens {
					if fcmList.RoleId == roles {
						tokens = append(tokens, fcmList.FcmTokens...)
					}
				}
			}
		}
		uniqueTokens := removeDuplicatesFromFcm(tokens)
		req.UniqueTargetsList = uniqueTokens
	}

	return label, req, fcmData
}

func sendFcmNotifications(req FCMRequest, fcmData map[string]string, uniqueTokens []string, label string, logger push.Logger) ([]external.NotificationResult, error) {
	start := time.Now()
	var results []external.NotificationResult
	var err error
	if !config.ServerConfig.TestEnv {
		batchSize := 500

		// Check if there are tokens to process.
		if len(uniqueTokens) == 0 {
			logger.Info().Msg("No valid tokens found to send FCM notifications.")
			return nil, nil
		}

		// If the token count exceeds the batch size, break it into chunks.
		if len(uniqueTokens) > batchSize {
			numChunks := int(math.Ceil(float64(len(uniqueTokens)) / float64(batchSize)))

			for i := 0; i < numChunks; i++ {
				start := i * batchSize
				end := (i + 1) * batchSize
				if end > len(uniqueTokens) {
					end = len(uniqueTokens)
				}

				chunk := uniqueTokens[start:end]
				logger.Info().Msgf("Processing FCM chunk %d/%d", i+1, numChunks)

				// Sending batch of notifications.
				if req.Campaign != nil {
					if results, err = external.SendFCMNotification(chunk, req.Subject, req.Body, req.Campaign.FcmImage, req.Data, label, req.Campaign.MessageType, logger); err != nil {
						return results, err
					}
				} else {
					startTime := time.Now()
					if results, err = external.SendFCMNotification(chunk, req.Subject, req.Body, "", fcmData, label, req.MessageType, logger); err != nil {
						logger.Error().Msgf("Error sending FCM notifications: %v", err)
						return results, err
					}
					logger.Info().Msgf("Total time taken for chunk %d/%d: %v", i+1, numChunks, time.Since(startTime))
				}
			}
		} else {
			// Single batch if the number of tokens is less than batch size.
			logger.Info().Msg("Processing single batch of FCM notifications.")

			if req.Campaign != nil {
				if results, err = external.SendFCMNotification(uniqueTokens, req.Subject, req.Body, req.Campaign.FcmImage, req.Data, label, req.Campaign.MessageType, logger); err != nil {
					return results, err
				}
			} else {
				startTime := time.Now()
				if results, err = external.SendFCMNotification(uniqueTokens, req.Subject, req.Body, "", fcmData, label, req.MessageType, logger); err != nil {
					logger.Error().Msgf("Error sending FCM notifications: %v", err)
					return results, err
				}
				logger.Info().Msgf("Total time taken for single batch: %v", time.Since(startTime))
			}
		}
	}
	if req.Campaign != nil {
		logger.Info().Msgf("FCM: time taken to send all notification for terminalId %v and campaignId: %v = %v miliseconds", req.Recipients[0].Name, req.Campaign.Id, time.Since(start).Milliseconds())
	}

	return results, err
}

func sanitizeString(input string) (string, error) {
	input = strings.TrimSpace(input)
	if len(input) > 40 {
		input = input[:40]
	}
	re, err := regexp.Compile(`[^a-zA-Z0-9]+`)
	if err != nil {
		return "", err
	}
	input = re.ReplaceAllString(input, "_")
	input = strings.ToLower(input)
	return input, nil
}

func removeDuplicatesFromFcm(fcmTokenList []string) []string {
	allKeys := make(map[string]bool)
	var list []string
	for _, item := range fcmTokenList {
		if _, value := allKeys[item]; !value {
			allKeys[item] = true
			list = append(list, item)
		}
	}
	return list
}

// LoginRequest represents the request body for login.
type LoginRequest struct {
	Email    string `json:"email"`
	Password string `json:"password"`
}

// HTTPError represents an error response.
type HTTPError struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
}

// handleLogin handles user login requests.
//
// @Summary User login
// @Description Logs in a user with email and password
// @Tags Authentication
// @Accept json
// @Produce json
// @Param login body LoginRequest true "Login credentials"
// @Success 200 {object} okResp "Successful login"
// @Failure 400 {object} HTTPError "Invalid input"
// @Failure 500 {object} HTTPError "Internal server error"
// @Router /api/login [post]
func handleLogin(c echo.Context) error {
	var (
		logger = c.Get("logger").(push.Logger)
		req    struct {
			Email    string `json:"email"`
			Password string `json:"password"`
		}
	)
	// Get and validate fields.
	if err := c.Bind(&req); err != nil {
		logger.Printf("error reading request body: %v", err)
		return err
	}
	resp := Login(req.Email, req.Password, c)

	return c.JSON(http.StatusOK, okResp{resp})
}

func handleMqttMessenger(c echo.Context) error {
	var (
		logger = c.Get("logger").(push.Logger)
		req    struct {
			Subject           string            `json:"subject"`
			Body              string            `json:"body"`
			Recipients        []recipient       `json:"recipients"`
			Campaign          *campaign         `json:"campaign"`
			Target            string            `json:"target,omitempty"`
			CacheDetails      map[string]string `json:"cacheDetails,omitempty"`
			UniqueTargetsList []string          `json:"unique_targets_list"`
		}
	)

	if err := c.Bind(&req); err != nil {
		logger.Error().Msgf("error reading request body: %v", err)
		return err
	}

	logger.Info().Msgf("recieved mqtt request for topic %v", req.Subject)

	for _, key := range req.UniqueTargetsList {
		topic := strings.Replace(req.Subject, "<imei>", key, -1)
		logger.Info().Msgf("sending message %v to topic %v", req.Body, topic)
		err := PublishToMqtt(topic, req.Body, logger)
		if err != nil {
			logger.Error().Msgf("error occured while publishing to mqtt broker for topic %s, detailed error %v", topic, err)
		}
	}
	return nil
}
